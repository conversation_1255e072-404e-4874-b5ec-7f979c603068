import MainImage from "@assets/product/v2-pim-5.png";
import FirstImage from "@assets/product/v2-pim-6.png";
import SecondImage from "@assets/product/v2-pim-7.png";
import ThirdImage from "@assets/product/v2-pim-8.png";
import FourthImage from "@assets/product/v2-pim-9.png";
import ProductPageTemplate from "@/components/Products/ProductPageTemplate";
import SEO from "@/components/ui/SEO";
import { TranslationContext } from "@/contexts/TranslationContext";
import { useTranslation } from "react-i18next";

export default function Pim() {
  const { t } = useTranslation();
  const translateKey = "products.pim";
  const descriptionImages = [FirstImage, SecondImage, ThirdImage, FourthImage];

  return (
    <TranslationContext.Provider value={{ translateKey }}>
      <SEO
        translateKey={translateKey}
        title={`${t(`${translateKey}.seo.meta_title`)}`}
        description={t(`${translateKey}.seo.meta_desc`)}
      />
      <ProductPageTemplate translateKey={translateKey} mainImage={MainImage} descriptionImages={descriptionImages} />
    </TranslationContext.Provider>
  );
}

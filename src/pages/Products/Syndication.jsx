import FirstImage from "@assets/product/v2-syndication-1.png";
import SecondImage from "@assets/product/v2-syndication-2.png";
import ThirdImage from "@assets/product/v2-syndication-3.png";
import FourthImage from "@assets/product/v2-syndication-4.png";
import ProductPageTemplate from "@/components/Products/ProductPageTemplate";
import SEO from "@/components/ui/SEO";
import { TranslationContext } from "@/contexts/TranslationContext";
import { useTranslation } from "react-i18next";

export default function Syndication() {
  const { t } = useTranslation();
  const translateKey = "products.syndication";
  const descriptionImages = [FirstImage, SecondImage, ThirdImage, FourthImage];

  return (
    <TranslationContext.Provider value={{ translateKey }}>
      <SEO
        translateKey={translateKey}
        title={`${t(`${translateKey}.seo.meta_title`)}`}
        description={t(`${translateKey}.seo.meta_desc`)}
      />
      <ProductPageTemplate translateKey={translateKey} descriptionImages={descriptionImages} showMainImage={false} />
    </TranslationContext.Provider>
  );
}

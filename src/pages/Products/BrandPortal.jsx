import FirstImage from "@assets/product/v2-brand_portal-1.png";
import SecondImage from "@assets/product/v2-brand_portal-2.png";
import ThirdImage from "@assets/product/v2-brand_portal-3.png";
import FourthImage from "@assets/product/v2-brand_portal-4.png";
import SEO from "@/components/ui/SEO";
import { TranslationContext } from "@/contexts/TranslationContext";
import { useTranslation } from "react-i18next";
import ProductPageTemplate from "@/components/Products/ProductPageTemplate";

export default function BrandPortal() {
  const { t } = useTranslation();
  const translateKey = "products.brand_portal";
  const descriptionImages = [FirstImage, SecondImage, ThirdImage, FourthImage];

  return (
    <TranslationContext.Provider value={{ translateKey }}>
      <SEO
        translateKey={translateKey}
        title={`${t(`${translateKey}.seo.meta_title`)}`}
        description={t(`${translateKey}.seo.meta_desc`)}
      />
      <ProductPageTemplate translateKey={translateKey} descriptionImages={descriptionImages} showMainImage={false} />
    </TranslationContext.Provider>
  );
}

import FirstImage from "@assets/product/v2-workflow-5.png";
import SecondImage from "@assets/product/v2-workflow-6.png";
import ThirdImage from "@assets/product/v2-workflow-7.png";
import FourthImage from "@assets/product/v2-workflow-8.png";
import SEO from "@/components/ui/SEO";
import { TranslationContext } from "@/contexts/TranslationContext";
import { useTranslation } from "react-i18next";
import ProductPageTemplate from "@/components/Products/ProductPageTemplate";

export default function Workflow() {
  const { t } = useTranslation();
  const translateKey = "products.workflow";
  const descriptionImages = [FirstImage, SecondImage, ThirdImage, FourthImage];

  return (
    <TranslationContext.Provider value={{ translateKey }}>
      <SEO
        translateKey={translateKey}
        title={`${t(`${translateKey}.seo.meta_title`)}`}
        description={t(`${translateKey}.seo.meta_desc`)}
      />
      <ProductPageTemplate translateKey={translateKey} descriptionImages={descriptionImages} showMainImage={false} />
    </TranslationContext.Provider>
  );
}

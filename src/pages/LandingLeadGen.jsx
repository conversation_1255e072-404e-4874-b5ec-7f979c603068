import { PagesContainerLanding } from "@/components/ui/common.styled";
import { Box, CardMedia, Chip, Container, Stack, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import SEO from "@/components/ui/SEO";
import webinaireImg from "@assets/photos/webinaire_img.jpg";

export default function LandingLeadGen() {
  const { t } = useTranslation();

  return (
    <>
      <SEO
        title={t("landing_lead_gen.seo.meta_title")}
        description={t("leanding_lead_gen.seo.meta_desc")}
      />
      <PagesContainerLanding>
        <Container maxWidth="md" sx={{ paddingTop: { xs: 4, xl: 6 } }}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 3,
              textAlign: "center",
            }}
          >
            <Chip color="secondary" label={"WEBINAIRE"} />
            <Typography variant="h1">
              [WEBINAIRE] - Développer vos ventes e-commerce avec le PIM & DAM !
            </Typography>
            <CardMedia component="img" image={webinaireImg} alt="Webinaire" />
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                gap: 1,
              }}
            >
              <Typography variant="h2">
                📅 SINFIN vous propose un webinaire unique qui vous apportera
                des réponses concrètes sur la gestion, l'optimisation et la
                diffusion de ces catalogues de produits.
              </Typography>
            </Box>
          </Box>
        </Container>
        <Container maxWidth="xl" sx={{ paddingTop: { xs: 2, xl: 2 } }}>
          <iframe
            width="100%"
            height="600"
            frameborder="0"
            src="https://app.livestorm.co/p/22c2d268-d666-46df-9796-6e58ad98d118/form"
            title="[WEBINAIRE] - Développer vos ventes e-commerce avec le PIM & DAM ! | SINFIN"
          />
        </Container>
      </PagesContainerLanding>
    </>
  );
}

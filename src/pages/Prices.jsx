import FirstSection from "@/components/Tarifs/FirstSection/FirstSection";
import { PagesContainer } from "@/components/ui/common.styled";
import { Container } from "@mui/material";
import FeaturesDetail from "@/components/Tarifs/FeaturesDetail/FeatureDetail";
import theme from "../theme/theme";
import SEO from "@/components/ui/SEO";
import { useTranslation } from "react-i18next";

export default function Prices() {
  const { t } = useTranslation();

  return (
    <>
      <SEO
        title={t("tarifs.seo.meta_title")}
        description={t("tarifs.seo.meta_desc")}
      />
      <PagesContainer sx={{
        paddingTop: theme.spacing(4),
        [theme.breakpoints.up("xl")]: { paddingTop: (theme) => theme.spacing(10) },
      }}>
        <Container maxWidth="xl">
          <FirstSection />
        </Container>
        <Container maxWidth="lg">
          <FeaturesDetail />
        </Container>
      </PagesContainer>
    </>
  );
}

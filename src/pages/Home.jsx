import SEO from "@/components/ui/SEO";
import Faq from "@/components/Home/Faq";
import FirstSection from "@/components/Home/FirstSection";
import Solutions from "@/components/Home/Solutions";
import StartNow from "@/components/Home/StartNow";
import { Discover, Features, News, Reviews } from "@/components/Layout";
import { PagesContainer } from "@/components/ui/common.styled";
import { Box, Container, useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";

export default function HomePage() {
  const theme = useTheme();
  const { t } = useTranslation();

  return (
    <>
      <SEO
        title={t("index.seo.meta_title")}
        description={t("index.seo.meta_desc")}
      />
      <PagesContainer>
        <Container maxWidth="lg">
          <FirstSection />
        </Container>
        <Container maxWidth="xl">
          <Solutions />
        </Container>
        <Container maxWidth="md">
          <StartNow />
        </Container>
        <Features />
        <Box
          sx={{
            backgroundColor: (theme) => theme.palette.primary[100],
          }}
        >
          <Container
            maxWidth="xl"
            sx={{
              paddingY: theme.spacing(10),
            }}
          >
            <Faq />
          </Container>
        </Box>
        <Reviews />
        <Container maxWidth="lg">
          <News />
        </Container>
        <Container maxWidth="lg">
          <Discover />
        </Container>
      </PagesContainer>
    </>
  );
}

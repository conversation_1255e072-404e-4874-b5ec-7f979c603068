import { Outlet, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import LANGS from "@/enums/LANGS.js";
import LayoutFooter from "@/components/Layout/Footer/LayoutFooter";
import NavbarLanding from "./NavBarLanding";

export default function LayoutLanding() {
  const { lang } = useParams();
  const { i18n } = useTranslation();
  const navigate = useNavigate();

  useEffect(() => {
    if (lang !== i18n.language) {
      if (Object.values(LANGS).includes(lang)) {
        void i18n.changeLanguage(lang);
      } else {
        navigate("/");
      }
    }
  }, [lang]);

  return (
    <div>
      <NavbarLanding />
      <Outlet />
      <LayoutFooter />
    </div>
  );
}

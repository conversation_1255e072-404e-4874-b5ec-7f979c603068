import SinfinLogo from "@assets/sinfin.svg";
import {
  AppB<PERSON>,
  Box,
  Button,
  Container,
  Toolbar,
  useTheme,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import ROUTES from "../../../enums/ROUTES.js";
import { Link } from "react-router-dom";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function NavbarLanding() {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const { url } = useRouting();

  return (
    <nav>
      <Toolbar />
      <AppBar
        elevation={0}
        sx={{
          backgroundColor: theme.palette.common.white,
          zIndex: (theme) => theme.zIndex.drawer + 1,
          borderBottom: (theme) => `solid 1px ${theme.palette.grey[300]}`,
        }}
      >
        <Toolbar
          sx={{
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Container
            maxWidth="xl"
            sx={{
              display: "flex",
              alignItems: "center",
              gap: theme.spacing(4),
              justifyContent: "space-between",
              padding: 0,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: theme.spacing(4),
              }}
            >
              <Link to={url(ROUTES.HOME, { lang: i18n.language })} title="Home">
                <img
                  src={SinfinLogo}
                  height={20}
                  alt="Sinfin logo"
                  aria-hidden={true}
                />
              </Link>
            </Box>
            <Button
              sx={{ display: { xs: "none", sm: "flex" } }}
              variant="contained"
              color="primary"
              component={Link}
              to={url(ROUTES.ASK_DEMO)}
            >
              {t("common.ask_demo")}
            </Button>
          </Container>
        </Toolbar>
      </AppBar>
    </nav>
  );
}

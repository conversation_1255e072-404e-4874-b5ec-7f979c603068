import CustomersList from "@/components/Home/CustomersList";
import { PagesContainerLanding } from "@/components/ui/common.styled";
import { Box, Container, Typography } from "@mui/material";
import FirstSection from "./FirstSection";
import { colorWords } from "@/utils/string.utils";
import { useTranslation } from "react-i18next";
import theme from "@/theme";
import Landing from "@/assets/images/landing/landing.png";
import SolutionsLanding from "./SolutionsLanding";
import { Discover } from "@/components/Layout/index.jsx";
import SEO from "@/components/ui/SEO";

export default function Presentation() {
  const { t } = useTranslation();

  return (
    <>
      <SEO
        title={t("landing.seo.meta_title")}
        description={t("landing.seo.meta_desc")}
      />
      <PagesContainerLanding>
        <Container maxWidth="xl" sx={{ paddingTop: { xs: 4, xl: 10 } }}>
          <FirstSection />
        </Container>
        <Container maxWidth="lg">
          <Typography
            sx={{ display: "block", textAlign: "center", margin: "auto" }}
            variant="h2"
            dangerouslySetInnerHTML={{
              __html: colorWords(
                t("landing.section.title"),
                t("landing.section.title_colored_words").split(" "),
                theme.palette.primary.main
              ),
            }}
          />
          <CustomersList />
        </Container>
        <Box maxWidth="xl" sx={{ display: "flex", margin: "auto" }}>
          <img
            src={Landing}
            style={{
              objectFit: "contain",
              width: "100%",
              "@media (min-width: 700px)": {
                height: "350px",
              },
              "@media (max-width: 700px)": {
                height: "100%",
              },
            }}
            alt={t("landing.alt_img")}
          />
        </Box>
        <Container maxWidth="xl">
          <SolutionsLanding />
        </Container>
        <Container maxWidth="lg">
          <Discover />
        </Container>
      </PagesContainerLanding>
    </>
  );
}

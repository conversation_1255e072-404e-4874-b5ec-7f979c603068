import FormLanding from "./FormLanding";
import { Grid, Typography } from "@mui/material";
import { ChipPrimaryContrast } from "@/components/ui/chips.styled";
import { ContentContainer } from "@/components/ui/common.styled";
import { useTranslation } from "react-i18next";
import { colorWords } from "@/utils/string.utils";
import theme from "@/theme";
import { SELSSY_PRESENTATION_PIM_VALUE } from "@/env";

export default function FirstSection() {
  const { t } = useTranslation();

  return (
    <>
      <Grid container justifyContent="space-between" spacing={3}>
        <Grid item xs={12} md>
          <ContentContainer sx={{ border: "none", gap: 3 }}>
            <ChipPrimaryContrast label={t("landing.tag")} />
            <Typography
              variant="h1"
              dangerouslySetInnerHTML={{
                __html: colorWords(
                  t("landing.title"),
                  t("landing.title_colored_words").split(" "),
                  theme.palette.primary.main
                ),
              }}
            />
            <Typography>{t("landing.description")}</Typography>
          </ContentContainer>
        </Grid>
        <Grid item xs={12} md={5}>
          <FormLanding sourceValue={SELSSY_PRESENTATION_PIM_VALUE} />
        </Grid>
      </Grid>
    </>
  );
}

import InputSkeleton from "@/components/ui/inputs/InputSkeleton";
import {
  <PERSON>ton,
  Checkbox,
  Divider,
  FormControlLabel,
  Grid,
  TextField,
  Typography,
} from "@mui/material";
import { useState } from "react";
import { ContentContainer } from "@/components/ui/common.styled";
import { sendToSelssy } from "@/utils/api.utils";
import { useTranslation } from "react-i18next";
import { SELSSY_SOURCE_ID } from "@/env.js";

export default function FormLanding({ sourceValue }) {
  const { t } = useTranslation();

  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [conditionChecked, setConditionChecked] = useState(false);

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      setIsSubmitting(true);

      const formValues = {
        sourceId: SELSSY_SOURCE_ID,
        sourceValue,
        note: `<strong>Société</strong> : ${e.target.elements.company.value}`,
        email: e.target.elements.email.value,
        lastName: e.target.elements.lastName.value,
        firstName: e.target.elements.firstName.value,
        phone: e.target.elements.phone.value,
      };

      const response = await sendToSelssy(formValues);
      const data = await response.json();

      if (response.ok) {
        setSuccess(true);
        setError(null);
        setConditionChecked(false);
        e.target.reset();
      } else {
        setSuccess(false);
        setError(
          data?.violations.map((violation) => violation.message).join(", ")
        );
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ContentContainer
      sx={{
        backgroundColor: "white",
        gap: 3,
        borderRadius: (theme) => theme.spacing(2),
        border: (theme) => `solid 1px ${theme.palette.secondary[200]}`,
      }}
    >
      <form onSubmit={handleSubmit}>
        {error && (
          <Typography pb={2} color="error" variant="body1">
            {error}
          </Typography>
        )}
        {success && (
          <Typography pb={2} color="green" variant="body1">
            {t("contact_us.form.success")}
          </Typography>
        )}
        <Grid container spacing={3}>
          <Grid item xs={12} xl={6}>
            <InputSkeleton label={t("contact_us.form.lastname.label")} required>
              <TextField
                placeholder={t("contact_us.form.lastname.placeholder")}
                required
                name="lastName"
              />
            </InputSkeleton>
          </Grid>
          <Grid item xs={12} xl={6}>
            <InputSkeleton
              label={t("contact_us.form.firstname.label")}
              required
            >
              <TextField
                placeholder={t("contact_us.form.firstname.placeholder")}
                required
                name="firstName"
              />
            </InputSkeleton>
          </Grid>
          <Grid item xs={12} xl={6}>
            <InputSkeleton label={t("contact_us.form.email.label")} required>
              <TextField
                placeholder={t("contact_us.form.email.placeholder")}
                type="email"
                required
                name="email"
              />
            </InputSkeleton>
          </Grid>
          <Grid item xs={12} xl={6}>
            <InputSkeleton label={t("contact_us.form.phone.label")}>
              <TextField
                placeholder={t("contact_us.form.phone.placeholder")}
                name="phone"
              />
            </InputSkeleton>
          </Grid>
          <Grid item xs={12}>
            <InputSkeleton label={t("contact_us.form.company.label")} required>
              <TextField
                placeholder={t("contact_us.form.company.placeholder")}
                required
                name="company"
              />
            </InputSkeleton>
          </Grid>
          <Grid item xs>
            <Divider />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={conditionChecked}
                  name="condition"
                  onChange={(e) => setConditionChecked(e.target.checked)}
                />
              }
              label={t("contact_us.form.condition")}
              slotProps={{
                typography: {
                  variant: "body2",
                },
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              type="submit"
              sx={{
                width: "100%",
              }}
              disabled={!conditionChecked || isSubmitting}
            >
              {t("common.ask_demo")}
            </Button>
          </Grid>
        </Grid>
      </form>
    </ContentContainer>
  );
}

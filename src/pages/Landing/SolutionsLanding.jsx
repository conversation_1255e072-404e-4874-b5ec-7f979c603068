import FeatureCard from "@/components/ui/cards/FeatureCard/FeatureCard";
import { Container, Grid, Stack, Typography, useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import { useData } from "@/contexts/DataProvider";

export default function SolutionsLanding() {
  const theme = useTheme();
  const { t } = useTranslation();
  const { url, trans } = useRouting();
  const { solutions } = useData();

  return (
    <Stack
      component={"section"}
      sx={{
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: theme.palette.primary[100],
        borderRadius: theme.spacing(2),
        [theme.breakpoints.up("xl")]: {
          padding: theme.spacing(10),
        },
      }}
    >
      <Container
        maxWidth="xl"
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: theme.spacing(3),
          textAlign: "center",
          padding: (theme) => `${theme.spacing(4)} ${theme.spacing(3)}`,
          [theme.breakpoints.up("xl")]: {
            gap: theme.spacing(6),
          },
        }}
      >
        <Container
          maxWidth="md"
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: theme.spacing(3),
          }}
        >
          <Typography variant="h2">{t("common.our_solutions")}</Typography>
        </Container>
        <Grid container spacing={{ xs: 0, xl: 4 }}>
          {solutions.map(solution => (
            <Grid key={solution.uuid} item xs={12} xl={4}>
              <FeatureCard
                title={trans(solution.title)}
                text={trans(solution.description)}
                image={solution.image}
                textButton={trans(solution.link)}
                href={url(solution.route)}
              />
            </Grid>
          ))}
        </Grid>
      </Container>
    </Stack>
  );
}

import SEO from "@/components/ui/SEO.jsx";
import { Container, Typography } from "@mui/material";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function Content({ content, maxWidth = "lg", children = null }) {
  const { trans } = useRouting();

  return (
    <>
      <SEO
        title={trans(content.meta_title)}
        description={trans(content.meta_description)}
      />
      <Container maxWidth={maxWidth} sx={{ marginTop: { xs: 4, xl: 10 } }}>
        {content.content ? (
          <Typography
            component="div"
            dangerouslySetInnerHTML={{ __html: trans(content.content) }}
          />
        ) : null}
        {children ? children : null}
      </Container>
    </>
  );
}

import { Grid, Typography } from "@mui/material";
import { ChipPrimaryContrast } from "@/components/ui/chips.styled";
import { ContentContainer } from "@/components/ui/common.styled";
import { useTranslation } from "react-i18next";
import { colorWords } from "@/utils/string.utils";
import theme from "@/theme";
import FormLanding from "../Landing/FormLanding";
import { SELSSY_PRESENTATION_DAM_VALUE } from "@/env";

export default function FirstSection() {
  const { t } = useTranslation();

  return (
    <>
      <Grid container justifyContent="space-between" spacing={3}>
        <Grid item xs={12} md>
          <ContentContainer sx={{ border: "none", gap: 3 }}>
            <ChipPrimaryContrast label={t("landing_dam.tag")} />
            <Typography
              variant="h1"
              dangerouslySetInnerHTML={{
                __html: colorWords(
                  t("landing_dam.title"),
                  t("landing_dam.title_colored_words").split(" "),
                  theme.palette.primary.main,
                ),
              }}
            />
            <Typography dangerouslySetInnerHTML={{ __html: t("landing_dam.description") }} />
          </ContentContainer>
        </Grid>
        <Grid item xs={12} md={5}>
          <FormLanding sourceValue={SELSSY_PRESENTATION_DAM_VALUE} />
        </Grid>
      </Grid>
    </>
  );
}

import { PagesContainerLanding } from "@/components/ui/common.styled";
import { Box, Container, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import FirstSection from "./FirstSection";
import { colorWords } from "@/utils/string.utils";
import theme from "@/theme";
import LandingDam from "@/assets/images/landing/landing-dam.png";
import CustomersList from "@/components/Home/CustomersList";
import SolutionsLanding from "../Landing/SolutionsLanding";
import { Discover } from "@/components/Layout";
import SEO from "@/components/ui/SEO";

export default function PresentationDam() {
  const { t } = useTranslation();

  return (
    <>
      <SEO
        title={t("landing_dam.seo.meta_title")}
        description={t("landing_dam.seo.meta_desc")}
      />
      <PagesContainerLanding>
        <Container maxWidth="xl" sx={{ paddingTop: { xs: 4, xl: 10 } }}>
          <FirstSection />
        </Container>
        <Container maxWidth="lg">
          <Typography
            sx={{ display: "block", textAlign: "center", margin: "auto" }}
            variant="h2"
            dangerouslySetInnerHTML={{
              __html: colorWords(
                t("landing.section.title"),
                t("landing.section.title_colored_words").split(" "),
                theme.palette.primary.main,
              ),
            }}
          />
          <CustomersList />
        </Container>
        <Box maxWidth="xl" sx={{ display: "flex", margin: "auto" }}>
          <img
            src={LandingDam}
            style={{
              objectFit: "contain",
              width: "100%",
              "@media (min-width: 700px)": {
                height: "350px",
              },
              "@media (max-width: 700px)": {
                height: "100%",
              },
            }}
            alt={t("landing.alt_img")}
          />
        </Box>
        <Container maxWidth="xl">
          <SolutionsLanding />
        </Container>
        <Container maxWidth="lg">
          <Discover />
        </Container>
      </PagesContainerLanding>
    </>
  );
}

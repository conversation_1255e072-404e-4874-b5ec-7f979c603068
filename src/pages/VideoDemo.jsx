import { Box, Button, Container, Grid, Typography, useTheme } from "@mui/material";
import { PagesContainer } from "@/components/ui/common.styled";
import SEO from "@/components/ui/SEO";
import { colorWords } from "@/utils/string.utils";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { useRouting } from "@/contexts/RoutingProvider";
import ROUTES from "@/enums/ROUTES";
import FeatureCard from "@/components/ui/cards/FeatureCard/FeatureCard";
import IconFeatureCard from "@/components/ui/cards/IconFeature/IconFeatureCard";
import LabelCard from "@/components/ui/cards/LabelCard/LabelCard";
import BlockDiscover from "@/components/BlockDiscover";
import CustomersList from "@/components/Home/CustomersList";

import Video from "/assets/videos/Rendu_4.mp4";
import DamImage from "@assets/product/v2-dam-3.png";
import DiffuserImage from "@assets/product/v2-diffuser-1.png";
import WorkflowImage from "@assets/product/v2-workflow-3.png";
import CentralizeIcon from "/assets/icons/centralized_data.svg";
import DiffuseIcon from "/assets/icons/distrib_auto.svg";
import OrdersIcon from "/assets/icons/grouped_orders.svg";
import CompleteIcon from "/assets/icons/product_sheet.svg";

export default function VideoDemo() {
  const theme = useTheme();
  const { t } = useTranslation();
  const { url } = useRouting();

  const colorizeText = (text, coloredWords) => ({
    dangerouslySetInnerHTML: {
      __html: colorWords(text, coloredWords.split(" "), theme.palette.primary[700])
    }
  });

  const demoButton = (
    <Button
      component={Link}
      to={url(ROUTES.ASK_DEMO)}
      sx={{ width: { xs: "100%", sm: "max-content" }, marginTop: 4 }}
    >
      {t("common.ask_demo")}
    </Button>
  );

  const features = [
    { text: t("video_demo.features.feature1.title"), image: DamImage },
    { text: t("video_demo.features.feature2.title"), image: DiffuserImage },
    { text: t("video_demo.features.feature3.title"), image: WorkflowImage }
  ];

  const iconFeatures = [
    { icon: CentralizeIcon, alt: "Centralize", title: t("video_demo.data_features.centralize.title"), description: t("video_demo.data_features.centralize.description") },
    { icon: DiffuseIcon, alt: "Diffuse", title: t("video_demo.data_features.diffusion.title"), description: t("video_demo.data_features.diffusion.description") },
    { icon: OrdersIcon, alt: "Order", title: t("video_demo.data_features.orders.title"), description: t("video_demo.data_features.orders.description") },
    { icon: CompleteIcon, alt: "Complete", title: t("video_demo.data_features.complete.title"), description: t("video_demo.data_features.complete.description") }
  ];

  const labelTypes = ["pim", "dam", "syndication", "orders"];

  const sectionTitle = (title, coloredWords, sx = {}) => (
    <Typography variant="h2" sx={{ marginTop: 8, ...sx }} {...colorizeText(t(title), t(coloredWords))} />
  );

  return (
    <PagesContainer>
      <SEO
        title={t("video_demo.seo.meta_title")}
        description={t("video_demo.seo.meta_desc")}
      />
      <Container maxWidth="xl">
        <Box sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          textAlign: "center",
          gap: 3,
          paddingY: { xs: 6, md: 10 }
        }}>
          <Typography
            variant="h1"
            sx={{ fontWeight: "bold", marginBottom: 2 }}
            {...colorizeText(t("video_demo.title"), t("video_demo.title_colored_words"))}
          />
          <Typography>{t("video_demo.subtitle")}</Typography>

          <Box sx={{
            width: "100%",
            maxWidth: "800px",
            marginTop: 4,
            "& video": {
              width: "100%",
              borderRadius: theme.spacing(2),
              cursor: "pointer"
            }
          }}>
            <video controls src={Video} />
          </Box>

          {demoButton}
          <CustomersList />

          {sectionTitle(
            "video_demo.manage_data", 
            "video_demo.manage_data_colored_words", 
            { fontWeight: "bold", marginTop: 6 }
          )}

          <Grid container spacing={{ xs: 3, xl: 4 }} sx={{ marginTop: 4 }}>
            {features.map((item, idx) => (
              <Grid item xs={12} xl={4} key={idx}>
                <FeatureCard title="" text={item.text} image={item.image} />
              </Grid>
            ))}
          </Grid>

          {demoButton}

          {sectionTitle("video_demo.structure_data", "video_demo.structure_data_colored_words")}

          <Grid container spacing={4} sx={{ marginTop: 4 }}>
            {iconFeatures.map((feature, idx) => (
              <Grid item xs={12} sm={6} md={6} key={idx}>
                <IconFeatureCard
                  icon={<img src={feature.icon} alt={feature.alt} width="60" height="60" />}
                  title={feature.title}
                  description={feature.description}
                />
              </Grid>
            ))}
          </Grid>

          {sectionTitle("video_demo.single_source_truth", "video_demo.single_source_truth_colored_words")}

          <Grid container spacing={4} sx={{ marginTop: 4 }}>
            {labelTypes.map(type => (
              <Grid item xs={12} sm={6} md={3} key={type}>
                <LabelCard
                  label={t(`video_demo.cards.${type}.label`)}
                  text={t(`video_demo.cards.${type}.text`)}
                  boldWord={t(`video_demo.cards.${type}.bold_word`)}
                  textAlign="left"
                  labelAlign="center"
                  chipColor="secondary"
                />
              </Grid>
            ))}
          </Grid>
          
          {demoButton}
          
          <Box sx={{ width: "100%", marginTop: 6 }}>
            <BlockDiscover />
          </Box>
        </Box>
      </Container>
    </PagesContainer>
  );
}
import React, { Fragment, useState } from "react";
import { Discover } from "@/components/Layout";
import FeatureCard from "@/components/ui/cards/FeatureCard/FeatureCard";
import { PagesContainer } from "@/components/ui/common.styled";
import {
  Box,
  Chip,
  Container,
  Divider,
  Grid,
  List,
  ListItem,
  ListItemButton,
  Typography,
  useTheme,
} from "@mui/material";
import ROUTES from "../enums/ROUTES.js";
import { useTranslation } from "react-i18next";
import { Link, useNavigate } from "react-router-dom";
import { colorWords } from "@/utils/string.utils.js";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import { useData } from "@/contexts/DataProvider.jsx";

export default function Resources() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { url, trans } = useRouting();
  const theme = useTheme();

  const categories = ["news", "case_studies", "all"];

  const [category, setCategory] = useState(null);
  const { resources } = useData();

  const resourcesFiltered = resources.filter((article) => null === category || article.tags.includes(category));

  return (
    <PagesContainer>
      <Container maxWidth="md">
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            textAlign: "center",
            gap: 3,
            paddingTop: 10,
          }}
        >
          <Chip label={t("news.tag")} color="secondary" />
          <Typography
            variant="h1"
            dangerouslySetInnerHTML={{
              __html: colorWords(
                t("layout.footer.news.title"),
                t("layout.footer.news.title_colored_words").split(" "),
                theme.palette.primary.main
              ),
            }}
          />
          <Typography>{t("news.subtitle")}</Typography>
        </Box>
      </Container>
      <Container maxWidth="xl">
        <Grid container spacing={{ xs: 4, xl: 20 }}>
          <Grid item xs={12} xl component="aside">
            <List component="nav" dense sx={{ position: "sticky", top: "100px" }}>
              {categories.map((c, i) => (
                <Fragment key={i}>
                  <ListItem disableGutters onClick={() => setCategory(c === "all" ? null : c)}>
                    <ListItemButton disableGutters disableTouchRipple>
                      <Typography variant="h6" color={c === category ? "primary" : "secondary.600"}>
                        {t(`resources_categories.${c}`)}
                      </Typography>
                    </ListItemButton>
                  </ListItem>
                  {i !== categories.length - 1 && <Divider />}
                </Fragment>
              ))}
              {/* <Divider />
              <ListItem disableGutters>
                <ListItemButton disableGutters component={Link} to={url(ROUTES.LANDING_LEAD_GEN)}>
                  <Typography variant="h6" color="secondary.600">
                    {t("resources.webinaire.title")}
                  </Typography>
                </ListItemButton>
              </ListItem> */}
              <Divider />
              <ListItem disableGutters>
                <ListItemButton disableGutters component={Link} to={url(ROUTES.RESOURCE_GLOSSAIRE)}>
                  <Typography variant="h6" color="secondary">
                    {t("resources.glossaire.title")}
                  </Typography>
                </ListItemButton>
              </ListItem>
            </List>
          </Grid>
          {/* <Grid item xs={12} xl /> */}
          <Grid item xl={8} container spacing={2}>
            {resourcesFiltered.map((resource) => (
              <Grid key={resource.uuid} item xs={12} md={6}>
                <FeatureCard
                  title={trans(resource.title)}
                  tags={resource.tags.map((tag) => t(`resources_categories.${tag}`))}
                  text={resource.text}
                  createdAt={resource.createdAt}
                  updatedAt={resource.updatedAt}
                  image={resource.image}
                  imageAlt={trans(resource.image_alt)}
                  cardMediaProps={{ sx: { height: 234 } }}
                  onClick={() =>
                    navigate(
                      resource.tags.includes("case_studies")
                        ? url(ROUTES.USECASE_SLUG, { slug: resource.slug })
                        : url(ROUTES.RESOURCES_SLUG, { slug: resource.slug })
                    )
                  }
                />
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Container>
      <Container maxWidth="lg">
        <Discover />
      </Container>
    </PagesContainer>
  );
}

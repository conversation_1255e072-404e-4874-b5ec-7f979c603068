import Background from "@assets/background/404-background.png";
import { Box, Chip, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

export default function PageNotFound() {
  const { t } = useTranslation();

  return (
    <Box sx={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "95dvh",
      width: "100vw",
      backgroundImage: `url(${Background})`,
    }}>
      <Box sx={{ textAlign: "center" }}>
        <Chip sx={{ backgroundColor: "primary.800", color: "white" }} label="404" />
        <Typography variant="h1" color="white">{t("404.title")}</Typography>
        <Typography color="white">{t("404.subtitle")}</Typography>
      </Box>
    </Box>
  );
}

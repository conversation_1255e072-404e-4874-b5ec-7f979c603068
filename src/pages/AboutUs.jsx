import CEO from "@assets/photos/ceo.png";
import { Discover } from "@/components/Layout";
import Quote from "@/components/ui/Quote";
import { PagesContainer } from "@/components/ui/common.styled";
import { Box, Container, Typography } from "@mui/material";
import Header from "@/components/AboutUs/Header";
import Innovation from "@/components/AboutUs/Innovation";
import Job from "@/components/AboutUs/Job";
import Partners from "@/components/AboutUs/Partners";
import TextDoubleColumns from "@/components/ui/TextDoubleColumns";
import { useTranslation } from "react-i18next";
import SEO from "@/components/ui/SEO";

export default function AboutUs() {
  const { t } = useTranslation();

  return (
    <>
      <SEO title={t("about_us.seo.meta_title")} description={t("about_us.seo.meta_desc")} />
      <PagesContainer>
        <Container maxWidth="lg">
          <Header />
        </Container>
        <Container maxWidth="xl">
          <TextDoubleColumns
            left={<Typography variant="h3">{t("about_us.article_1.title")}</Typography>}
            right={
              <>
                <Typography>{t("about_us.article_1.text_1")}</Typography>
                <br />
                <Typography>{t("about_us.article_1.text_2")}</Typography>
                <br />
                <Typography>{t("about_us.article_1.text_3")}</Typography>
                <br />
              </>
            }
          />
        </Container>
        <Container maxWidth="md">
          <Quote
            text={t("about_us.quote.text")}
            author="Jeremy Domingo"
            image={CEO}
            post={t("about_us.quote.author_job")}
          />
        </Container>
        <Container maxWidth="xl">
          <Innovation />
        </Container>
        <Container maxWidth="xl">
          <Job />
        </Container>
        <Box sx={{ backgroundColor: (theme) => theme.palette.primary[100] }}>
          <Container maxWidth="md" sx={{ paddingY: 10 }}>
            <Partners />
          </Container>
        </Box>
        <Container maxWidth="lg">
          <Discover />
        </Container>
      </PagesContainer>
    </>
  );
}

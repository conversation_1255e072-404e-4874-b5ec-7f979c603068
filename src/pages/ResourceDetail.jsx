import SEO from "@/components/ui/SEO";
import ResourcesPageTemplate from "@/components/Resources/ResourcesPageTemplate.jsx";
import ROUTES from "@/enums/ROUTES";
import { useRouting } from "@/contexts/RoutingProvider";
import { Navigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useData } from "@/contexts/DataProvider";

export default function ResourceDetail() {
  const { t } = useTranslation();
  const { url, trans } = useRouting();
  const { slug } = useParams();
  const { resources } = useData();

  if (!resources.length) {
    return null;
  }

  const resource = resources.find(r => r.slug === slug);
  if (!resource) {
    return <Navigate to={url(ROUTES.HOME)} />;
  }

  return (
    <>
      <SEO
        title={trans(resource.meta_title)}
        description={trans(resource.meta_description)}
      />
      <ResourcesPageTemplate
        breadcrumb={[
          { label: t("common.home"), href: "/" },
          { label: t(`resources_categories.${resource.tags[0]}`), href: url(ROUTES.RESOURCES) },
          { label: trans(resource.title) },
        ]}
        resource={resource}
      />
    </>
  );
}

import ImageSolution from "@assets/Solution/V2-international-main.png";
import SolutionsPageTemplate from "@/components/Solutions/SolutionsPageTemplate";
import SEO from "@/components/ui/SEO";
import { useTranslation } from "react-i18next";

export default function International() {
  const { t } = useTranslation();
  const mainImage = ImageSolution;
  const translateKey = "solutions.international";

  return (
    <>
      <SEO title={`${t(`${translateKey}.seo.meta_title`)}`} description={t(`${translateKey}.seo.meta_desc`)} />
      <SolutionsPageTemplate translateKey={translateKey} mainImage={mainImage} subtitle={false} titlelist={false} />
    </>
  );
}

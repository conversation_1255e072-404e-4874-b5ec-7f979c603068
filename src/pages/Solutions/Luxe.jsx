import ImageSolution from "@assets/Solution/V2-Luxe.png";
import FirstImage from "@assets/Solution/V2-Luxe2.png";
import SolutionsPageWithContentImageTemplate from "@/components/Solutions/SolutionsPageWithContentImageTemplate";
import SEO from "@/components/ui/SEO";
import { useTranslation } from "react-i18next";

export default function Luxe() {
  const { t } = useTranslation();
  const mainImage = ImageSolution;
  const translateKey = "solutions.luxe";
  const descriptionImages = [FirstImage];

  return (
    <>
      <SEO title={`${t(`${translateKey}.seo.meta_title`)}`} description={t(`${translateKey}.seo.meta_desc`)} />
      <SolutionsPageWithContentImageTemplate
        translateKey={translateKey}
        mainImage={mainImage}
        descriptionImages={descriptionImages}
      />
    </>
  );
}

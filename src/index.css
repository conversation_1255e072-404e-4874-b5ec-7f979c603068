@font-face {
  font-family: "BeVietnamPro";
  src: url("./assets/images/fonts/Be_Vietnam_Pro/BeVietnamPro-Regular.ttf") format("truetype");
  font-weight: 400;
}
@font-face {
  font-family: "BeVietnamPro";
  src: url("./assets/images/fonts/Be_Vietnam_Pro/BeVietnamPro-SemiBold.ttf") format("truetype");
  font-weight: 600;
}
@font-face {
  font-family: "BeVietnamPro";
  src: url("./assets/images/fonts/Be_Vietnam_Pro/BeVietnamPro-Bold.ttf") format("truetype");
  font-weight: 700;
}
@font-face {
  font-family: "OpenSans";
  src: url("./assets/images/fonts/Open_Sans/OpenSans-Regular.ttf") format("truetype");
}
@font-face {
  font-family: "OpenSans";
  src: url("./assets/images/fonts/Open_Sans/OpenSans-SemiBold.ttf") format("truetype");
  font-weight: 600;
}
@font-face {
  font-family: "OpenSans";
  src: url("./assets/images/fonts/Open_Sans/OpenSans-Bold.ttf") format("truetype");
  font-weight: 700;
}
.link {
  cursor: pointer;
}
.link:hover {
  color: #008fee;
}
html {
  overflow-x: hidden;
}
html,
body {
  width: 100%;
  padding: 0;
  margin: 0;
  background-color: #fbfcff;
}
a {
  text-decoration: none;
  color: inherit;
}
.underline-links a {
  text-decoration: underline;
}
li::marker {
  color: #006cb4;
  border-radius: 2px;
}
.slick-slide {
  cursor: grab;
}
@media screen and (min-width: 1312px) {
  .slick-slide {
    filter: blur(1px) grayscale(0.25);
  }
  .slick-current + div {
    filter: blur(0);
  }
}


.white-marker-li::marker {
  color: white;
}
import palette from "./palette";
import typography from "./typography";

export default {
  MuiButton: {
    styleOverrides: {
      root: {
        borderRadius: 8,
        padding: "8px 24px 8px 24px",
        textTransform: "none",
        fontWeight: "bold",
        width: "max-content",
        "&.MuiButton-contained": {
          "&.MuiButton-containedSecondary": {
            backgroundColor: palette.secondary[200],
            color: palette.common.black,
            "&:hover": {
              backgroundColor: palette.secondary[300],
            },
          },
        },
        "&.MuiButton-text": {
          paddingRight: 0,
          paddingLeft: 0,
          "&:hover": {
            color: palette.primary[500],
            backgroundColor: "transparent",
          },
        },
      },
    },
    defaultProps: {
      variant: "contained",
      disableElevation: true,
    },
  },
  Paper: {
    defaultProps: {
      elevation: 0,
    },
  },
  MuiBreadcrumbs: {
    styleOverrides: {
      li: {
        color: palette.secondary[500],
        a: {
          textDecoration: "none",
          color: "inherit",
        },
        "&:last-child": {
          color: palette.common.black,
        },
      },
    },
  },
  MuiTabs: {
    styleOverrides: {
      root: {
        backgroundColor: palette.primary[200],
        borderRadius: "12px",
        padding: "8px",
        gap: "16px",
        minHeight: "0",
      },
    },
    defaultProps: {
      indicatorColor: "none",
    },
  },
  MuiTab: {
    styleOverrides: {
      root: {
        borderRadius: "12px",
        padding: "8px 16px",
        fontWeight: "bold",
        color: palette.primary[700],
        textTransform: "none",
        minHeight: "0",
        "&.Mui-selected": {
          backgroundColor: palette.common.white,
          color: palette.primary[700],
          boxShadow:
            "0px 0px 6px rgba(2, 6, 15, 0.07), 0px 0px 6px rgba(2, 6, 15, 0.07)",
        },
      },
    },
  },
  MuiPagination: {
    defaultProps: {
      shape: "rounded",
    },
    styleOverrides: {
      root: {
        borderRadius: "8px",
        padding: "8px",
      },
    },
  },
  MuiPaginationItem: {
    styleOverrides: {
      root: {
        fontWeight: "bold",
        "&.Mui-selected	": {
          backgroundColor: palette.primary[100],
          color: palette.primary[700],
        },
      },
    },
  },
  MuiIcon: {
    styleOverrides: {
      root: {
        color: palette.text.body,
      },
    },
  },
  MuiChip: {
    styleOverrides: {
      root: {
        width: "max-content",
        fontWeight: "bold",
        "&.MuiChip-colorPrimary": {
          backgroundColor: palette.primary[100],
          color: palette.primary[800],
        },
        "&.MuiChip-colorSecondary": {
          backgroundColor: palette.secondary[100],
          color: palette.secondary[900],
        },
      },
    },
  },
  MuiCard: {
    defaultProps: {
      variant: "outlined",
    },
    styleOverrides: {
      root: {
        border: "none",
        textAlign: "start",
        borderRadius: 16,
      },
    },
  },
  MuiCardMedia: {
    styleOverrides: {
      root: {
        borderRadius: 16,
      },
    },
  },
  MuiCardActionArea: {
    styleOverrides: {
      root: {
        borderRadius: 16,
      },
    },
  },
  MuiTableCell: {
    styleOverrides: {
      root: {
        borderBottom: "none",
        color: palette.secondary[700],
        "&.MuiTableCell-head	": {
          fontSize: typography.h5.fontSize,
          fontWeight: "bold",
          borderBottom: `solid 1px ${palette.secondary[200]}`,
          color: palette.common.black,
        },
      },
    },
  },
  MuiTableRow: {
    styleOverrides: {
      root: {
        borderBottom: `solid 1px ${palette.secondary[200]}`,
      },
    },
  },
  MuiTextField: {
    defaultProps: {
      variant: "outlined",
    },
  },
  MuiOutlinedInput: {
    styleOverrides: {
      root: {
        borderRadius: 16,
        backgroundColor: palette.common.white,
        ".MuiOutlinedInput-input	": {
          padding: "8px 12px",
        },
      },
    },
  },
  MuiFormLabel: {
    styleOverrides: {
      root: {
        color: palette.secondary[900],
        fontWeight: "bold",
      },
    },
  },
  MuiLink: {
    styleOverrides: {
      root: {
        color: "inherit",
        textDecoration: "none",
        "&:hover": {
          color: palette.primary.main,
        },
      },
    },
  },
  MuiAccordionDetails: {
    styleOverrides: {
      root: {
        fontFamily: "OpenSans",
        fontSize: "16px",
        fontWeight: "regular",
        lineHeight: "24px",
        color: palette.text.body,
      },
    },
  },
  MuiAlert: {
    variants: [
      {
        props: { severity: "error" },
        style: {
          border: "1px solid red",
        },
      },
      {
        props: { severity: "success" },
        style: {
          border: "1px solid green",
        },
      },
    ],
  },
};

import { createTheme } from "@mui/material";
import components from "./components";
import palette from "./palette";
import typography from "./typography";

const themeConfig = {
  components,
  palette,
  typography,
  shadows: {
    0: "none",
    1: "0px 2px 4px rgba(2, 6, 15, 0.07), 0px 1px 2px rgba(2, 6, 15, 0.07)",
    2: "0px 4px 8px rgba(2, 6, 15, 0.04), 0px 2px 4px rgba(2, 6, 15, 0.04), 0px 1px 2px rgba(2, 6, 15, 0.04)",
    3: "0px 8px 16px rgba(2, 6, 15, 0.04), 0px 4px 8px rgba(2, 6, 15, 0.04), 0px 2px 4px rgba(2, 6, 15, 0.04), 0px 1px 2px rgba(2, 6, 15, 0.04)",
    4: "0px 16px 32px rgba(2, 6, 15, 0.04), 0px 8px 16px rgba(2, 6, 15, 0.04), 0px 4px 8px rgba(2, 6, 15, 0.04), 0px 2px 4px rgba(2, 6, 15, 0.04), 0px 1px 2px rgba(2, 6, 15, 0.04)",
    5: "0px 32px 64px rgba(2, 6, 15, 0.04), 0px 16px 32px rgba(2, 6, 15, 0.04), 0px 8px 16px rgba(2, 6, 15, 0.04), 0px 4px 8px rgba(2, 6, 15, 0.04), 0px 2px 4px rgba(2, 6, 15, 0.04), 0px 1px 2px rgba(2, 6, 15, 0.04)",
    6: "0px 32px 64px rgba(2, 6, 15, 0.04), 0px 16px 32px rgba(2, 6, 15, 0.04), 0px 8px 16px rgba(2, 6, 15, 0.04), 0px 4px 8px rgba(2, 6, 15, 0.04), 0px 2px 4px rgba(2, 6, 15, 0.04), 0px 1px 2px rgba(2, 6, 15, 0.04)",
    16: "0px 32px 64px rgba(2, 6, 15, 0.04), 0px 16px 32px rgba(2, 6, 15, 0.04), 0px 8px 16px rgba(2, 6, 15, 0.04), 0px 4px 8px rgba(2, 6, 15, 0.04), 0px 2px 4px rgba(2, 6, 15, 0.04), 0px 1px 2px rgba(2, 6, 15, 0.04)",
  },
  spacing: 8,
  shape: {
    borderRadius: 8,
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 864,
      lg: 1088,
      xl: 1312,
      "2xl": 1440,
    },
  },
};

export default createTheme(themeConfig);

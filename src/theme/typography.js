import { createTheme } from "@mui/material/styles";
import palette from "./palette";
const defaultTheme = createTheme();

export default {
  fontFamily: 'OpenSans',
  h1: {
    fontFamily: "BeVietnamPro",
    fontSize: "48px",
    fontWeight: "bold",
    lineHeight: "56px",
    color: palette.text.heading,
    [defaultTheme.breakpoints.down("xl")]: {
      fontSize: "40px",
      lineHeight: "56px",
    },
  },
  h2: {
    fontFamily: "BeVietnamPro",
    fontSize: "32px",
    fontWeight: "bold",
    lineHeight: "40px",
    color: palette.text.heading,
    [defaultTheme.breakpoints.down("xl")]: {
      fontSize: "28px",
      lineHeight: "40px",
    },
  },
  h3: {
    fontFamily: "BeVietnamPro",
    fontSize: "24px",
    fontWeight: "bold",
    lineHeight: "32px",
    color: palette.text.heading,
  },
  h4: {
    fontFamily: "BeVietnamPro",
    fontSize: "20px",
    fontWeight: "bold",
    lineHeight: "24px",
    color: palette.text.heading,
  },
  h5: {
    fontFamily: "BeVietnamPro",
    fontSize: "18px",
    fontWeight: "bold",
    lineHeight: "24px",
    color: palette.text.heading,
  },
  h6: {
    fontFamily: "BeVietnamPro",
    fontSize: "16px",
    fontWeight: "bold",
    lineHeight: "24px",
    color: palette.text.heading,
  },
  body1: {
    fontFamily: "OpenSans",
    fontSize: "16px",
    fontWeight: "regular",
    lineHeight: "24px",
    color: palette.text.body,
  },
  body2: {
    fontFamily: "OpenSans",
    fontSize: "14px",
    fontWeight: "regular",
    lineHeight: "20px",
    color: palette.text.body,
  },
  caption: {
    fontFamily: "OpenSans",
    fontSize: "12px",
    fontWeight: "regular",
    lineHeight: "16px",
  },
  bold: {
    fontFamily: "BeVietnamPro",
    fontSize: "16px",
    fontWeight: "bold",
    lineHeight: "16px",
    color: palette.text.body,
  },
};

import ROUTES from "./enums/ROUTES.js";
import LANGS from "./enums/LANGS.js";
import { resources } from "./data.js";

const urls = ["https://www.sinfin.fr"];

Object.keys(ROUTES).map(routeKey => {
  const route = ROUTES[routeKey];
  Object.values(LANGS).map(lang => {
    if (Object.prototype.hasOwnProperty.call(route, lang)) {
      const uri = route[lang].replace(":lang", lang);
      if (uri.includes(":")) {
        if ("RESOURCES_SLUG" === routeKey) {
          resources.filter(r => !r.useCase).map(r => {
            urls.push(`https://www.sinfin.fr${uri.replace(":slug", r.slug)}`);
          });
        } else if ("USECASE_SLUG" === routeKey) {
          resources.filter(r => !!r.useCase).map(r => {
            urls.push(`https://www.sinfin.fr${uri.replace(":slug", r.slug)}`);
          });
        }
      } else {
        urls.push(`https://www.sinfin.fr${uri}`);
      }
    }
  });
});

const now = new Date().toISOString();

const sitemapUrls = urls.map(url => `  <url>
    <loc>${url}</loc>
    <lastmod>${now}</lastmod>
  </url>`).join("\n");

const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset
  xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd"
>
${sitemapUrls}
</urlset>`;

console.log(sitemap);

import ReviewCard from "@/components/ui/cards/ReviewCard";
import { ChipPrimaryContrast } from "@/components/ui/chips.styled";
import { Grid, Typography } from "@mui/material";
import { ContentContainer } from "@/components/ui/common.styled";
import FormDiscover from "./FormDiscover.jsx";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import { useTranslation } from "react-i18next";
import { useData } from "@/contexts/DataProvider.jsx";
import GoogleCalendarButton from "./GoogleCalendarButton.jsx";

export default function BlockDiscover() {
  const { t } = useTranslation();
  const { trans } = useRouting();
  const { reviews } = useData();

  const review = reviews.find((review) => review.isDefault);

  if (!review) return null;

  return (
    <Grid container justifyContent="space-between" spacing={3}>
      <Grid item xs={12} md>
        <ContentContainer
          sx={{
            backgroundColor: "primary.100",
            border: "none",
            gap: 3,
          }}
        >
          <ChipPrimaryContrast label={t("ask_demo.tag")} />
          <Typography variant="h1">{t("ask_demo.title")}</Typography>
          <Typography>{t("ask_demo.subtitle")}</Typography>
          <ReviewCard
            image={review.image}
            title=""
            text={trans(review.review)}
            author={{
              name: review.author,
              post: trans(review.job),
            }}
          />
          <GoogleCalendarButton />
        </ContentContainer>
      </Grid>
      <Grid item xs={12} md={5}>
        <FormDiscover />
      </Grid>
    </Grid>
  );
}

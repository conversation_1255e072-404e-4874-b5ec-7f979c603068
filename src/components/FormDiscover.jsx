import InputSkeleton from "@/components/ui/inputs/InputSkeleton";
import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControlLabel,
  Grid,
  TextField,
  Typography,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { ContentContainer } from "@/components/ui/common.styled";
import { sendToSelssy } from "@/utils/api.utils";
import { SELSSY_DEMO_VALUE, SELSSY_SOURCE_ID } from "@/env.js";

export default function FormDiscover() {
  const { t } = useTranslation();

  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [conditionChecked, setConditionChecked] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    let formValues = {
      sourceId: SELSSY_SOURCE_ID,
      sourceValue: SELSSY_DEMO_VALUE,
      note: "",
    };

    let options = [];

    for (const field of e.target.elements) {
      if (field.name && field.type !== "checkbox") {
        formValues[field.name] = field.value;
      } else if (
        field.type === "checkbox" &&
        field.checked &&
        field.name.startsWith("options_demo_")
      ) {
        options.push(field.labels[0].innerText);
      }
    }

    formValues.note = `<strong>Société</strong> : ${
      formValues.company
    }<br/><strong>Options</strong> : ${options.join(", ")}`;

    try {
      const response = await sendToSelssy(formValues);
      const data = await response.json();

      if (response.ok) {
        setSuccess(true);
        setError(null);
        setConditionChecked(false);
        e.target.reset();
      } else {
        setSuccess(false);
        setError(
          data?.violations.map((violation) => violation.message).join(", ")
        );
      }
    } catch (error) {
      console.log(error)
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ContentContainer
      sx={{
        textAlign: "left",
        backgroundColor: "white",
        gap: 3,
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 1,
        }}
      >
        <Typography variant="h4">{t("ask_demo.form.title")}</Typography>
        <Typography>{t("ask_demo.form.subtitle")}</Typography>
      </Box>
      <form onSubmit={handleSubmit}>
        {error && (
          <Typography pb={2} color="error" variant="body1">
            {error}
          </Typography>
        )}

        {success && (
          <Typography pb={2} color="green" variant="body1">
            {t("ask_demo.form.success")}
          </Typography>
        )}
        <Grid container spacing={3}>
          <Grid item xs={12} xl={6}>
            <InputSkeleton label={t("ask_demo.form.lastname.label")} required>
              <TextField
                placeholder={t("ask_demo.form.lastname.placeholder")}
                required
                name="lastName"
              />
            </InputSkeleton>
          </Grid>
          <Grid item xs={12} xl={6}>
            <InputSkeleton label={t("ask_demo.form.firstname.label")} required>
              <TextField
                placeholder={t("ask_demo.form.firstname.placeholder")}
                required
                name="firstName"
              />
            </InputSkeleton>
          </Grid>
          <Grid item xs={12} xl={6}>
            <InputSkeleton label={t("ask_demo.form.email.label")} required>
              <TextField
                placeholder={t("ask_demo.form.email.placeholder")}
                type="email"
                required
                name="email"
              />
            </InputSkeleton>
          </Grid>
          <Grid item xs={12} xl={6}>
            <InputSkeleton label={t("ask_demo.form.phone.label")}>
              <TextField
                placeholder={t("ask_demo.form.phone.placeholder")}
                name="phone"
              />
            </InputSkeleton>
          </Grid>
          <Grid item xs={12}>
            <InputSkeleton label={t("ask_demo.form.company.label")} required>
              <TextField
                placeholder={t("ask_demo.form.company.placeholder")}
                required
                name="company"
              />
            </InputSkeleton>
          </Grid>
          <Grid item xs={12}>
            <InputSkeleton label={t("ask_demo.form.options_demo.label")}>
              {t("ask_demo.form.options_demo.options", {
                returnObjects: true,
              }).map((option, index) => (
                <FormControlLabel
                  key={index}
                  control={
                    <Checkbox
                      name={`options_demo_${index}`}
                      sx={{ padding: "4px" }}
                    />
                  }
                  label={option}
                />
              ))}
            </InputSkeleton>
          </Grid>
          <Grid item xs>
            <Divider />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  name="condition"
                  checked={conditionChecked}
                  onChange={(e) => setConditionChecked(e.target.checked)}
                />
              }
              label={t("ask_demo.form.condition")}
              slotProps={{
                typography: {
                  variant: "body2",
                },
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              type="submit"
              sx={{
                width: "100%",
              }}
              disabled={!conditionChecked || isSubmitting}
            >
              {t("ask_demo.form.btn_submit")}
            </Button>
          </Grid>
        </Grid>
      </form>
    </ContentContainer>
  );
}

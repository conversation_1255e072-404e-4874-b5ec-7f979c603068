import FirstSection from "@/components/Solutions/FirstSection";
import BannerQuoteLink from "@/components/ui/BannerQuoteLink";
import SolutionContentWithImage from "./SolutionContentWithImage";
import { PagesContainer } from "@/components/ui/common.styled";
import { Container, useTheme } from "@mui/material";
import { colorWords } from "@/utils/string.utils";
import { useTranslation } from "react-i18next";

export default function SolutionsPageWithContentImageTemplate({
  translateKey = "solutions",
  mainImage,
  firstSectionProps,
  descriptionImages = [],
}) {
  const theme = useTheme();
  const { t } = useTranslation();
  console.log(translateKey);

  const list = t(`${translateKey}.banner.listitems`, { returnObjects: true });
  const description = t(`${translateKey}.first_section.description`, { returnObjects: true });
  const firstSectionBoldWords = t(`${translateKey}.first_section.bold_words`, { returnObjects: true });
  const titleList = t(`${translateKey}.banner.titlelist`, { returnObjects: true });
  const descriptionContentImage = t(`${translateKey}.content.description`, { returnObjects: true });

  return (
    <PagesContainer>
      <Container maxWidth="xl">
        <FirstSection
          title={colorWords(
            t(`${translateKey}.first_section.title`),
            t(`${translateKey}.first_section.title_colored_words`),
            theme.palette.primary.main
          )}
          subtitle={t(`${translateKey}.first_section.subtitle`)}
          description={description}
          image={{
            src: mainImage,
            alt: t(`${translateKey}.first_section.alt_img`),
          }}
          tag={t(`${translateKey}.first_section.tag`)}
          boldWords={firstSectionBoldWords}
          {...firstSectionProps}
        />
      </Container>
      <Container maxWidth="xl">
        <SolutionContentWithImage
          title={t(`${translateKey}.content.title`)}
          description={descriptionContentImage}
          imageOnLeft={true}
          image={descriptionImages[0]}
        />
      </Container>
      <Container maxWidth="md">
        <BannerQuoteLink title={t(`${translateKey}.banner.title`)} titlelist={titleList} listitems={list} />
      </Container>
    </PagesContainer>
  );
}

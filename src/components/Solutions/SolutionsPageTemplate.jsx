import FirstSection from "@/components/Solutions/FirstSection";
import BannerQuote from "@/components/ui/BannerQuote";
import CardGrid from "@/components/Solutions/CardGrid";
import { PagesContainer } from "@/components/ui/common.styled";
import { Container, useTheme } from "@mui/material";
import { colorWords } from "@/utils/string.utils";
import { useTranslation } from "react-i18next";

export default function SolutionsPageTemplate({
  translateKey = "solutions",
  mainImage,
  firstSectionProps,
  subtitle,
  titlelist,
  cardGridItemPerRow = 4,
}) {
  const theme = useTheme();
  const { t } = useTranslation();

  const list = t(`${translateKey}.banner.listitems`, { returnObjects: true });
  const description = t(`${translateKey}.first_section.description`, { returnObjects: true });
  const cardTitles = t(`${translateKey}.card_grid.title`, { returnObjects: true });
  const cardSubtitles = t(`${translateKey}.card_grid.subtitle`, { returnObjects: true });
  const cardGridTitle = t(`${translateKey}.card_grid.main_title`);
  const firstSectionBoldWords = t(`${translateKey}.first_section.bold_words`, { returnObjects: true });
  const bannerBoldWords = t(`${translateKey}.banner.bold_words`, { returnObjects: true });
  const cardGridBoldWords = t(`${translateKey}.card_grid.bold_words`, { returnObjects: true });

  const cardItems = cardTitles.map((title, index) => ({
    title,
    content: cardSubtitles[index] || "",
  }));

  return (
    <PagesContainer>
      <Container maxWidth="xl">
        <FirstSection
          title={colorWords(
            t(`${translateKey}.first_section.title`),
            t(`${translateKey}.first_section.title_colored_words`),
            theme.palette.primary.main
          )}
          subtitle={t(`${translateKey}.first_section.subtitle`)}
          description={description}
          image={{
            src: mainImage,
            alt: t(`${translateKey}.first_section.alt_img`),
          }}
          tag={t(`${translateKey}.first_section.tag`)}
          boldWords={firstSectionBoldWords}
          {...firstSectionProps}
        />
      </Container>
      <Container maxWidth="md">
        <BannerQuote
          title={t(`${translateKey}.banner.title`)}
          subtitle={subtitle ? t(`${translateKey}.banner.subtitle`) : false}
          titlelist={titlelist ? t(`${translateKey}.banner.titlelist`) : false}
          boldWords={bannerBoldWords}
          listitems={list}
        />
      </Container>
      <Container maxWidth="xl">
        <CardGrid title={cardGridTitle} boldWords={cardGridBoldWords} items={cardItems} itemsPerRow={cardGridItemPerRow} />
      </Container>
    </PagesContainer>
  );
}

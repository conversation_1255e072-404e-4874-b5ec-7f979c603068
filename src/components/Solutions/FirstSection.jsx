import { Box, Chip, Grid, Paper, Typography, useTheme } from "@mui/material";

export default function FirstSection({ tag, title, subtitle, description, image, boldWords }) {
  const theme = useTheme();

  const formatDescription = (text, boldWordsArray) => {
    if (!text || !boldWordsArray || !boldWordsArray.length) return text;

    let formattedText = text;

    boldWordsArray.forEach((phrase) => {
      const regex = new RegExp(`\\b${phrase}\\b`, "g");
      formattedText = formattedText.replace(regex, `<strong>${phrase}</strong>`);
    });

    return formattedText;
  };

  return (
    <Box
      sx={{
        [theme.breakpoints.up("lg")]: {
          marginTop: theme.spacing(10),
        },
      }}
    >
      <Grid
        container
        sx={{
          display: "flex",
          flexDirection: "column",
          flexWrap: "nowrap",
          gap: theme.spacing(2),
          [theme.breakpoints.up("lg")]: {
            flexDirection: "row",
            justifyContent: "space-between",
          },
          padding: "32px 0",
        }}
      >
        <Grid item lg={6} display="flex" flexDirection="column" gap={theme.spacing(2)}>
          <Chip color="primary" label={tag} />
          <Typography
            variant="h1"
            dangerouslySetInnerHTML={{
              __html: title,
            }}
          />
          <Typography
            variant="h4"
            dangerouslySetInnerHTML={{
              __html: subtitle,
            }}
          />
          {description?.map((option, i) => (
            <Typography
              key={i}
              variant="body1"
              color="inherit"
              mt={1}
              dangerouslySetInnerHTML={{
                __html: boldWords
                  ? formatDescription(option, Array.isArray(boldWords) ? boldWords : boldWords.split(" "))
                  : option,
              }}
            />
          ))}
        </Grid>
        <Grid item xs={12} lg={5}>
          <Paper
            elevation={0}
            sx={{
              maxWidth: "100%",
              backgroundColor: "transparent",
            }}
          >
            <img {...image} width="100%" height={"100%"} />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

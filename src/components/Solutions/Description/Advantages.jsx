import LinkButton from "@/components/ui/LinkButton";
import { Grid, Typography } from "@mui/material";
import ROUTES from "../../../enums/ROUTES.js";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function Advantages({ advantages, title, icon, link, image, route = null, alt }) {
  const { url } = useRouting();

  return (
    <Grid container spacing={4}>
      <Grid item xs={12} lg={6}>
        <img style={{ borderRadius: "20px", objectFit: "contain" }} src={image} width={"100%"} alt={alt} />
      </Grid>
      <Grid
        item
        xs={12}
        lg={6}
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 2,
        }}
      >
        <Typography
          variant="h4"
          sx={{
            display: "flex",
            gap: 1,
            alignItems: "center",
          }}
        >
          {icon}
          {title}
        </Typography>
        <ul
          style={{
            margin: 0,
            paddingLeft: 20,
          }}
        >
          {advantages.map((advantage, index) => (
            <li key={index}>
              <Typography>{advantage}</Typography>
            </li>
          ))}
        </ul>
        <LinkButton text={link} to={url(route ?? ROUTES.PIM)} sx={{ color: "primary.900" }} />
      </Grid>
    </Grid>
  );
}

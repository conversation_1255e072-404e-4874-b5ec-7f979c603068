import { ContentContainer } from "@/components/ui/common.styled";
import { Box, Container, Tab, Tabs, Typography } from "@mui/material";
import { useState } from "react";
import TabPanel from "@/components/ui/TabPanel";
import Advantages from "./Advantages";

export default function Description({ title, subtitle, sections }) {
  const [tabActive, setTabActive] = useState(0);

  return (
    <ContentContainer
      sx={{
        padding: {
          xs: 0,
          sm: 4,
          xl: 10,
        },
        paddingY: {
          xs: 4,
        },
        backgroundColor: (theme) => theme.palette.primary[100],
      }}
    >
      <Container
        maxWidth="xl"
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: 6,
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 4,
            marginBottom: (theme) => theme.spacing(4),
            maxWidth: {
              xs: "100%",
              xl: "640px",
            },
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: (theme) => theme.spacing(4),
              textAlign: "center",
            }}
          >
            <Typography
              variant="h2"
              dangerouslySetInnerHTML={{
                __html: title,
              }}
            />
            <Typography>{subtitle}</Typography>
          </Box>
          <Box
            width={"100%"}
            display={{
              xs: "none",
              sm: "block",
            }}
          >
            <Tabs
              variant="fullWidth"
              value={tabActive}
              onChange={(e, newValue) => setTabActive(newValue)}
            >
              {sections.map((s, index) => (
                <Tab
                  key={index}
                  label={s.label}
                  icon={s.icon}
                  iconPosition={"start"}
                />
              ))}
            </Tabs>
          </Box>
        </Box>
        <Box
          display={{
            xs: "none",
            sm: "block",
          }}
        >
          {sections.map((s, index) => (
            <TabPanel key={index} value={tabActive} index={index}>
              <Advantages
                title={s.label}
                advantages={s.advantages}
                link={s.link}
                route={s.route}
                image={s.image}
                alt={s.alt}
              />
            </TabPanel>
          ))}
        </Box>
        <Box
          sx={{
            display: {
              xs: "flex",
              sm: "none",
            },
            flexDirection: "column",
            gap: 4,
          }}
        >
          {sections.map((s, index) => (
            <Advantages
              key={index}
              title={s.label}
              advantages={s.advantages}
              link={s.link}
              image={s.image}
              alt={s.alt}
            />
          ))}
        </Box>
      </Container>
    </ContentContainer>
  );
}

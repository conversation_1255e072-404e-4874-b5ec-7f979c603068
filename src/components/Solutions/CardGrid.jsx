import { Card, CardContent, Grid, Typography, Stack } from "@mui/material";

export default function CardGrid({ title, items, itemsPerRow, boldWords = [] }) {
  const formatText = (text, boldWordsArray) => {
    if (!text || !Array.isArray(boldWordsArray) || boldWordsArray.length === 0) return text;


    const escapeRegExp = (string) => {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    };

    let formattedText = text;

    boldWordsArray.forEach((phrase) => {
      const escapedPhrase = escapeRegExp(phrase);
      const regex = new RegExp(escapedPhrase, "g");
      formattedText = formattedText.replace(regex, `<strong>${phrase}</strong>`);
    });

    return formattedText;
  };
  return (
    <Stack mb={4} gap={2}>
      <Typography
        variant="h2"
        dangerouslySetInnerHTML={{
          __html: title,
        }}
      />
      <Grid container spacing={3}>
        {items.map((i, index) => (
          <Grid item xs={12} lg={12 / itemsPerRow} key={index}>
            <Card
              sx={{
                border: (theme) => `1px solid ${theme.palette.secondary[200]}`,
                height: "100%",
              }}
            >
              <CardContent
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 1,
                  padding: 4,
                }}
              >
                <Typography variant="h4">{i.title}</Typography>
                <Typography
                  dangerouslySetInnerHTML={{
                    __html: formatText(i.content, boldWords),
                  }}
                />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Stack>
  );
}

import { Box, Grid, Typography } from "@mui/material";

export default function SolutionContentWithImage({ title, description, image, imageOnLeft = true }) {
  return (
    <Box sx={{ mb: { xs: 15, md: 19 } }}>
      <Grid
        container
        sx={{
          flexDirection: { xs: "column", md: imageOnLeft ? "row-reverse" : "row" },
          gap: { xs: 4, md: 6 },
          alignItems: "flex-start",
        }}
      >
        <Grid item xs={12} md={5.5}>
          <Typography variant="h3" sx={{ mb: 3 }}>
            {title}
          </Typography>
          {description?.map((option, i) => (
            <Typography key={i} variant="body1" color="inherit" sx={{ mb: 2 }}>
              {option}
            </Typography>
          ))}
        </Grid>

        <Grid
          item
          xs={12}
          md={5.5}
          sx={{
            display: "flex",
            justifyContent: { xs: "center", md: imageOnLeft ? "flex-start" : "flex-end" },
            ml: { md: imageOnLeft ? 0 : "auto" },
            mr: { md: imageOnLeft ? "auto" : 0 },
          }}
        >
          <Box
            component="img"
            src={image}
            alt={title}
            sx={{ width: "100%", height: "auto", borderRadius: 2, objectFit: "cover" }}
          />
        </Grid>
      </Grid>
    </Box>
  );
}

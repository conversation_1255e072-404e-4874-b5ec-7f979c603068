import FeatureCard from "@/components/ui/cards/FeatureCard/FeatureCard";
import { Box, Container, Grid, Typography } from "@mui/material";

export default function Description({ title, subtitle, advantages }) {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: 6,
      }}
    >
      <Container sx={{ display: "flex", flexDirection: "column", gap: 3 }} maxWidth="md">
        <Typography
          variant="h2"
          dangerouslySetInnerHTML={{ __html: title }}
          sx={{ textAlign: "center" }} />
        <Typography dangerouslySetInnerHTML={{ __html: subtitle }} />
      </Container>
      <Grid container spacing={4}>
        {advantages.map((a, i) => (
          <Grid item xs={12} md={6} key={i}>
            <FeatureCard
              {...a}
              cardMediaProps={{
                sx: {
                  height: 330,
                },
                ...a.cardMediaProps,
              }}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}

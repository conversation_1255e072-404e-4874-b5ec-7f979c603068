import { Box, Container, Chip, Typography, useTheme } from "@mui/material";

export default function FirstSection({ image, tag, title, subtitle, showImage = true }) {
  const theme = useTheme();

  return (
    <Box>
      <Box
        sx={{
          position: "relative",
          paddingBottom: (theme) => theme.spacing(5),
          paddingTop: (theme) => theme.spacing(4),
          [theme.breakpoints.up("lg")]: {
            paddingTop: (theme) => theme.spacing(10),
          },
        }}
      >
        <Container
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: (theme) => theme.spacing(5),
          }}
        >
          <Box
            sx={{
              textAlign: "center",
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center",
              gap: (theme) => theme.spacing(3),
            }}
          >
            <Chip label={tag} color="secondary" />
            <Typography
              variant="h1"
              dangerouslySetInnerHTML={{
                __html: title,
              }}
            />
            <Typography>{subtitle}</Typography>
          </Box>
        </Container>
      </Box>
      {showImage && (
        <Box sx={{ position: "relative" }}>
          <Container sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}>
            <Box sx={{ zIndex: 2 }}>
              <img {...image} width="100%" height={"100%"} alt={"Banner image"} />
            </Box>
          </Container>
        </Box>
      )}
    </Box>
  );
}

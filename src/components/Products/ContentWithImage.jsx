import { Box, Grid, Typography } from "@mui/material";

export default function ContentWithImage({
  title,
  description,
  image,
  imageOnLeft = true,
  boldWords = [],
  linkWord = "",
  linkUrl = "",
}) {
  const desc = Array.isArray(description) ? description : [description];

  const formatText = (text, boldWordsArray, linkWord, linkUrl) => {
    if (!text) return text;

    const escapeRegExp = (string) => {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    };

    let formattedText = text;

    if (Array.isArray(boldWordsArray) && boldWordsArray.length > 0) {
      boldWordsArray.forEach((phrase) => {
        if (!phrase) return;
        const escapedPhrase = escapeRegExp(phrase).replace(/\\ /g, "\\s+");
        const regex = new RegExp(escapedPhrase, "gi");
        formattedText = formattedText.replace(regex, `<strong>${phrase}</strong>`);
      });
    }

    if (linkWord && linkUrl && linkUrl !== "#") {
      const escapedLinkWord = escapeRegExp(linkWord).replace(/\\ /g, "\\s+");
      const regex = new RegExp(`\\b${escapedLinkWord}\\b`, "gi");
      formattedText = formattedText.replace(
        regex,
        `<a href="${linkUrl}" style="color: inherit; text-decoration: underline;">${linkWord}</a>`
      );
    }

    return formattedText;
  };

  return (
    <Box sx={{ mb: { xs: 15, md: 19 } }}>
      <Grid
        container
        sx={{
          flexDirection: { xs: "column", md: imageOnLeft ? "row-reverse" : "row" },
          gap: { xs: 4, md: 6 },
          alignItems: "flex-start",
        }}
      >
        <Grid item xs={12} md={5.5}>
          <Typography variant="h3" sx={{ mb: 3 }}>
            {title}
          </Typography>
          {desc.map((p, i) => (
            <Typography
              key={i}
              variant="body1"
              sx={{ mb: 2 }}
              dangerouslySetInnerHTML={{
                __html: formatText(p, boldWords, linkWord, linkUrl),
              }}
            />
          ))}
        </Grid>

        <Grid
          item
          xs={12}
          md={5.5}
          sx={{
            display: "flex",
            justifyContent: { xs: "center", md: imageOnLeft ? "flex-start" : "flex-end" },
            ml: { md: imageOnLeft ? 0 : "auto" },
            mr: { md: imageOnLeft ? "auto" : 0 },
          }}
        >
          <Box
            component="img"
            src={image}
            alt={title}
            sx={{ width: "100%", height: "auto", borderRadius: 2, objectFit: "cover" }}
          />
        </Grid>
      </Grid>
    </Box>
  );
}

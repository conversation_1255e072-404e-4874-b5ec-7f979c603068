import FakeImage from "@assets/fakeImage.png";
import GridBackground from "@assets/background/grid-background-dark.png";
import CloudIcon from "@assets/icons/cloud.svg?react";
import SecurityIcon from "@assets/icons/security.svg?react";
import { Box, Container, Grid, Typography, useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";
import CardIcon from "@/components/ui/cards/CardIcon/CardIcon";

export default function Security() {
  const { t } = useTranslation();
  const theme = useTheme();

  return (
    <Box
      sx={{
        backgroundColor: (theme) => theme.palette.primary[900],
        backgroundImage: `url(${GridBackground})`,
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
        display: "flex",
        flexDirection: "column",
        gap: (theme) => theme.spacing(4),
        [theme.breakpoints.up("xl")]: {
          marginTop: theme.spacing(10),
          padding: theme.spacing(10),
        },
      }}
    >
      <Container
        maxWidth="xl"
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: (theme) => theme.spacing(4),
          padding: (theme) => theme.spacing(4),
          color: (theme) => theme.palette.common.white,
        }}
      >
        <Container
          maxWidth="md"
          sx={{
            textAlign: "center",
            display: "flex",
            flexDirection: "column",
            gap: (theme) => theme.spacing(4),
            alignItems: "center",
          }}
        >
          <Typography
            variant="h2"
            sx={{
              display: "flex",
              alignItems: "center",
              gap: (theme) => theme.spacing(1),
              color: "inherit",
            }}
          >
            <SecurityIcon />
            {t("products.security.title")}
          </Typography>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              gap: 2,
            }}
          >
            <Box sx={{ borderRadius: 20 }}>
              <img src={FakeImage} height={"100%"} width={"100%"} />
            </Box>
            <Box sx={{ borderRadius: 20 }}>
              <img src={FakeImage} height={"100%"} width={"100%"} />
            </Box>
            <Box sx={{ borderRadius: 20 }}>
              <img src={FakeImage} height={"100%"} width={"100%"} />
            </Box>
          </Box>
          <Typography color="inherit">{t("products.security.desc")}</Typography>
        </Container>
        <Grid container spacing={4}>
          {t("products.security.advantages", { returnObjects: true }).map(
            (a, i) => (
              <Grid item xs={12} md={6} key={i}>
                <CardIcon
                  context="dark"
                  title={a.title}
                  text={a.desc}
                  icon={<CloudIcon />}
                />
              </Grid>
            ),
          )}
        </Grid>
      </Container>
    </Box>
  );
}

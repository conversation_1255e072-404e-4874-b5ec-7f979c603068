import FirstSection from "@/components/Products/FirstSection";
import { PagesContainer } from "@/components/ui/common.styled";
import { Container, useTheme } from "@mui/material";
import { colorWords } from "@/utils/string.utils";
import { useTranslation } from "react-i18next";
import ContentWithImage from "@/components/Products/ContentWithImage";
import ROUTES from "../../enums/ROUTES.js";

export default function ProductPageTemplate({
  translateKey = "products",
  mainImage,
  descriptionImages = [],
  showMainImage = true,
  firstSectionProps,
}) {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const lang = i18n.language.toLowerCase();

  const getRouteUrl = (routeKey, lang) => {
    if (ROUTES[routeKey] && ROUTES[routeKey][lang]) {
      return ROUTES[routeKey][lang].replace(":lang", lang);
    }
    return "#";
  };

  const sections = t(`${translateKey}.content.sections`, { returnObjects: true }) || {};
  const sectionsCount = Object.keys(sections).length;

  const splitDescription = (k) => {
    const desc = t(`${translateKey}.content.sections.${k}.description`);
    if (!desc) return "";
    const parts = desc.split(/\.\s+/).map((p) => (p.endsWith(".") ? p : `${p}.`));
    return parts.length <= 2
      ? parts
      : [parts.slice(0, Math.ceil(parts.length / 2)).join(" "), parts.slice(Math.ceil(parts.length / 2)).join(" ")];
  };

  const getBoldWords = (path) => {
    const boldWords = t(path, { returnObjects: true });
    return Array.isArray(boldWords) ? boldWords : [];
  };

  return (
    <PagesContainer>
      <FirstSection
        image={{ src: mainImage, alt: t(`${translateKey}.first_section.alt_img`) }}
        tag={t(`${translateKey}.first_section.tag`)}
        title={colorWords(
          t(`${translateKey}.first_section.title`),
          t(`${translateKey}.first_section.title_colored_words`),
          theme.palette.primary.main
        )}
        subtitle={t(`${translateKey}.first_section.subtitle`)}
        showImage={showMainImage}
        {...firstSectionProps}
      />

      <Container maxWidth="xl">
        {!sectionsCount && descriptionImages[0] ? (
          <ContentWithImage
            title={t(`${translateKey}.content.title`)}
            description={t(`${translateKey}.content.description`)}
            image={descriptionImages[0]}
            boldWords={getBoldWords(`${translateKey}.content.bold_words`)}
          />
        ) : (
          sectionsCount > 0 &&
          descriptionImages.slice(0, sectionsCount).map((img, idx) => {
            let linkWord = "";
            try {
              linkWord = t(`${translateKey}.content.sections.${idx + 1}.link_word`);
              if (linkWord && linkWord.includes(".link_word")) {
                linkWord = "";
              }
            } catch (e) {
              linkWord = "";
            }

            let linkUrl = "#";
            if (linkWord) {
              if (linkWord === "PIM") {
                linkUrl = getRouteUrl("PIM", lang);
              } else if (linkWord === "DAM") {
                linkUrl = getRouteUrl("DAM", lang);
              }
            }

            console.log("🔗 LINK INFO:", {
              idx,
              linkWord,
              lang,
              url: linkUrl,
              currentRoute: window.location.pathname,
            });

            return (
              <ContentWithImage
                key={idx}
                title={t(`${translateKey}.content.sections.${idx + 1}.title`)}
                description={splitDescription(idx + 1)}
                image={img}
                imageOnLeft={idx % 2 === 0}
                boldWords={getBoldWords(`${translateKey}.content.sections.${idx + 1}.bold_words`)}
                linkWord={linkWord}
                linkUrl={linkUrl}
              />
            );
          })
        )}
      </Container>
    </PagesContainer>
  );
}

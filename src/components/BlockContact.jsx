import FormContact from "./FormContact.jsx";
import { useTranslation } from "react-i18next";
import { Grid, Typography } from "@mui/material";
import { ChipPrimaryContrast } from "@/components/ui/chips.styled";
import { ContentContainer } from "@/components/ui/common.styled";
import { Helmet } from "react-helmet";
import GoogleCalendarButton from "./GoogleCalendarButton.jsx";

export default function BlockContact() {
  const { t } = useTranslation();

  return (
    <>
      <Helmet>
        <link href="https://calendar.google.com/calendar/scheduling-button-script.css" rel="stylesheet" />
      </Helmet>
      <Grid container justifyContent="space-between" spacing={3}>
        <Grid item xs={12} md>
          <ContentContainer
            sx={{
              backgroundColor: "primary.100",
              border: "none",
              gap: 3,
            }}
          >
            <ChipPrimaryContrast label={t("contact_us.tag")} />
            <Typography variant="h1">{t("contact_us.title")}</Typography>
            <Typography>{t("contact_us.subtitle")}</Typography>
            <GoogleCalendarButton />
          </ContentContainer>
        </Grid>
        <Grid item xs={12} md={5}>
          <FormContact />
        </Grid>
      </Grid>
    </>
  );
}

import { Box } from "@mui/material";
import styled from "@emotion/styled";

export const ContentContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  borderRadius: theme.spacing(3),
  // border: `solid 1px ${theme.palette.secondary[200]}`,
  padding: theme.spacing(3),
}));

export const PagesContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(10),
  [theme.breakpoints.up("md")]: {
    gap: theme.spacing(15),
  },
}));

export const PagesContainerLanding = styled(Box)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(10),
  [theme.breakpoints.up("md")]: {
    gap: theme.spacing(7),
  },
}));

export const LogoContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  height: 24,
  width: 24,
  border: `solid 1px ${theme.palette.secondary[200]}`,
  borderRadius: theme.spacing(1),
  padding: theme.spacing(1),
  cursor: "pointer",
}));

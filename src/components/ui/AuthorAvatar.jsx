import { Box, Typography, useTheme } from "@mui/material";

export default function AuthorAvatar({ image, name, post }) {
  const theme = useTheme();
  return (
    <Box
      sx={{
        display: "flex",
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          color: theme.palette.text.body,
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: "600" }}>
          {name}
        </Typography>
        <Typography>{post}</Typography>
      </Box>
    </Box>
  );
}

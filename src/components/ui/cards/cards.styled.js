import { Card } from "@mui/material";
import styled from "@emotion/styled";

export const CardIconStyled = styled(Card, {
  shouldForwardProp: (prop) => prop !== "context",
})(({ theme, context }) => ({
  maxWidth: "100%",
  display: "flex",
  borderRadius: theme.spacing(3),
  padding: theme.spacing(3),
  backgroundColor:
    context === "dark"
      ? theme.palette.primary[800]
      : theme.palette.common.white,
  border:
    context === "dark" ? "unset" : `1px solid ${theme.palette.primary[200]}`,
  color: context === "dark" ? theme.palette.common.white : "inherit",
  [theme.breakpoints.down("xl")]: {
    flexDirection: "column",
  },
}));

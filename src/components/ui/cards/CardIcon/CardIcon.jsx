import { Box, CardActionArea, CardContent, Typography, useTheme } from "@mui/material";
import { CardIconStyled } from "@/components/ui/cards/cards.styled";

export default function CardIcon({ onClick, context = "light", ...props }) {
  return (
    <CardIconStyled context={context}>
      {onClick ? (
        <CardActionArea onClick={onClick}>
          <Content context={context} {...props} />
        </CardActionArea>
      ) : (
        <Content context={context} {...props} />
      )}
    </CardIconStyled>
  );
}

const Content = ({ icon, title, text, context }) => {
  const theme = useTheme();

  return (
    <>
      <CardContent
        sx={{
          paddingRight: 0,
          paddingLeft: 0,
          display: "flex",
          flexDirection: "column",
          gap: (theme) => theme.spacing(3),
          [theme.breakpoints.up("xl")]: {
            flexDirection: "row",
          },
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            width: "max-content",
            height: "max-content",
            borderRadius: (theme) => theme.spacing(2),
            padding: (theme) => theme.spacing(2),
            backgroundColor:
              context === "dark"
                ? (theme) => theme.palette.primary[700]
                : (theme) => theme.palette.common.light,
          }}
        >
          {icon}
        </Box>
        <Box>
          <Typography
            gutterBottom
            variant="h5"
            sx={{
              whiteSpace: "wrap",
              color: "inherit",
            }}
          >
            {title}
          </Typography>
          <Typography color="inherit">{text}</Typography>
        </Box>
      </CardContent>
    </>
  );
};

import TeamMember from "@/components/ui/AuthorAvatar";
import { Box, Card, CardContent, Typography, useTheme } from "@mui/material";

export default function ReviewCard({ image, title, text, author, alt }) {
  const theme = useTheme();

  return (
    <Card
      sx={{
        border: (theme) => `solid 1px ${theme.palette.secondary[200]}`,
        backgroundColor: (theme) => theme.palette.common.white,
        padding: (theme) => theme.spacing(2),
        borderRadius: (theme) => theme.spacing(2),
      }}
    >
      <CardContent
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: (theme) => theme.spacing(2),
        }}
      >
        <Box>
          <img src={image} height={56} alt={alt} />
          <Typography style={{ marginTop: "14px" }} variant="h5">{title}</Typography>
          <Typography color="text.secondary">{text}</Typography>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-end",
            [theme.breakpoints.down("md")]: {
              flexDirection: "column",
              alignItems: "flex-start",
              gap: 2,
            },
          }}
        >
          <TeamMember {...author} />
        </Box>
      </CardContent>
    </Card>
  );
}

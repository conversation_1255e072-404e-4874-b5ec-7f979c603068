import LinkButton from "@/components/ui/LinkButton";
import { Card<PERSON>ontent, CardMedia, Chip, Typography } from "@mui/material";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { toDate } from "@/utils/date.utils.js";

export default function Content({
  image,
  imageAlt = "",
  cardMediaProps,
  tags,
  title,
  text,
  createdAt,
  updatedAt,
  textButton,
  onClick,
  href,
}) {
  const { t } = useTranslation();

  return (
    <>
      {image ? (
        <CardMedia
          image={image}
          title={imageAlt}
          sx={{ height: 160 }}
          {...cardMediaProps}
        />
      ) : null}
      <CardContent
        sx={{
          paddingRight: 0,
          paddingLeft: 0,
          display: "flex",
          flexDirection: "column",
          gap: 1,
        }}
      >
        {tags?.length > 0 &&
          tags.map((chip) => (
            <Chip key={chip} label={chip} color="secondary" size="small" />
          ))}
        <Typography
          gutterBottom
          variant="h5"
          sx={{
            overflow: "hidden",
            display: "-webkit-box",
            WebkitLineClamp: 2,
            WebkitBoxOrient: "vertical",
          }}
        >
          {title}
        </Typography>
        {text ? (
          <Typography dangerouslySetInnerHTML={{ __html: text }} />
        ) : null}
        {updatedAt ? (
          <Typography variant="caption" color="textSecondary">
            {t("common.updated_at")} {toDate(updatedAt)}
          </Typography>
        ) : createdAt ? (
          <Typography variant="caption" color="textSecondary">
            {toDate(createdAt)}
          </Typography>
        ) : null}
        {textButton ? (
          <LinkButton
            text={""}
            color="inherit"
            sx={{ padding: (theme) => theme.spacing(1) }}
            onClick={onClick}
            component={href ? Link : "button"}
            to={href}
          >
            {textButton}
          </LinkButton>
        ) : null}
      </CardContent>
    </>
  );
}

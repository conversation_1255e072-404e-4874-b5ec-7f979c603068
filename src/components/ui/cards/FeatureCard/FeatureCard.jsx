import { Card, CardActionArea } from "@mui/material";
import Content from "./Content";

export default function FeatureCard({ onClick, ...props }) {
  return (
    <Card sx={{
      background: "transparent",
      maxWidth: "100%",
      flexGrow: 1,
    }}
    >
      {onClick ? (
        <CardActionArea onClick={onClick}>
          <Content {...props} />
        </CardActionArea>
      ) : (
        <Content {...props} />
      )}
    </Card>
  );
}

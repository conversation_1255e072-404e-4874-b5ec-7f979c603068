// src/components/ui/cards/IconFeature/IconFeatureCard.jsx
import { Box, Typography } from "@mui/material";

export default function IconFeatureCard({ icon, title, description }) {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        textAlign: "center",
        height: "100%",
        gap: (theme) => theme.spacing(3),
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          width: "80px",
          height: "80px",
          borderRadius: "16px",
          backgroundColor: (theme) => theme.palette.primary[100],
          "& svg": {
            color: (theme) => theme.palette.primary.main,
            fontSize: "2.5rem"
          }
        }}
      >
        {icon}
      </Box>
      <Typography 
        variant="h5"
        sx={{ 
          fontWeight: 600,
        }}
      >
        {title}
      </Typography>
      <Typography 
        variant="body1"
        color="text.secondary"
        sx={{
          lineHeight: 1.7,
          maxWidth: "90%",
        }}
      >
        {description}
      </Typography>
    </Box>
  );
}
import { Box, Chip, Typography } from "@mui/material";

export default function LabelCard({
  label,
  text,
  boldWord,
  textAlign = "left",
  labelAlign = "center",
  chipColor = "secondary",
}) {
  const words = text.split(" ");
  const firstWordIndex = words.findIndex((word) => word === boldWord);

  const formattedText = words.map((word, index) =>
    index === firstWordIndex ? <strong key={index}>{word} </strong> : <span key={index}>{word} </span>
  );

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 2,
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: labelAlign === "right" ? "flex-end" : labelAlign === "center" ? "center" : "flex-start",
        }}
      >
        <Chip label={label} color={chipColor} />
      </Box>
      <Typography sx={{ textAlign }}>{formattedText}</Typography>
    </Box>
  );
}

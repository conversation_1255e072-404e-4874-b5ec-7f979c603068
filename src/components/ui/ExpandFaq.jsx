import { Accordion, AccordionDetails, AccordionSummary, Typography, useTheme } from "@mui/material";
import MoreIcon from "@assets/icons/add_plus.svg";
import LessIcon from "@assets/icons/remove_minus.svg";
import { useState } from "react";

export default function ExpandFaq({ title, content }) {
  const [open, setOpen] = useState(false);
  const theme = useTheme();

  return (
    <Accordion
      elevation={0}
      onChange={() => setOpen(!open)}
      sx={{
        border: `solid 1px ${theme.palette.secondary[200]}`,
        borderRadius: "16px !important",
      }}
    >
      <AccordionSummary expandIcon={<img src={open ? LessIcon : MoreIcon} alt={"expand icon"} />}>
        <Typography
          sx={{
            color: theme.palette.text.body,
            fontWeight: "600",
          }}
        >
          {title}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        {content}
      </AccordionDetails>
    </Accordion>
  );
}

import InputSkeleton from "./InputSkeleton.jsx";
import { Select } from "@mui/material";
import InputBase from "@mui/material/InputBase";
import styled from "@emotion/styled";

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  "label + &": {
    marginTop: theme.spacing(1),
  },
  "& .MuiInputBase-input": {
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.mode === "light" ? "#f3f6f9" : "#1a2027",
    border: "1px solid",
    borderColor: theme.palette.mode === "light" ? "#e0e3e7" : "#2d3843",
    fontSize: 16,
    width: "auto",
    padding: "10px 12px",
  },
}));

const SelectCustom = styled(Select)(({ theme }) => ({
  border: `1px solid ${theme.palette.secondary?.[200]}`,
  borderRadius: "12px",
}));

export default function SelectInput({
                                      label,
                                      name,
                                      options = [],
                                      formControlProps,
                                      ...rest
                                    }) {
  return (
    <InputSkeleton label={label} {...formControlProps}>
      <Select native>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </Select>
    </InputSkeleton>
  );
}

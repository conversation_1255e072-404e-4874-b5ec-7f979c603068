import DoubleQuotesIcon from "@assets/icons/double_quotes.svg?react";
import { Box, Typography, useTheme } from "@mui/material";
import { ContentContainer } from "./common.styled";

export default function BannerQuote({ title, subtitle = false, listitems, titlelist = false, boldWords = [] }) {
  const theme = useTheme();

  const formatText = (text, boldWordsArray) => {
    if (!text || !Array.isArray(boldWordsArray) || boldWordsArray.length === 0) return text;


    const escapeRegExp = (string) => {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    };

    let formattedText = text;

    boldWordsArray.forEach(phrase => {
      const escapedPhrase = escapeRegExp(phrase);
      const regex = new RegExp(escapedPhrase, 'g');
      formattedText = formattedText.replace(regex, `<strong>${phrase}</strong>`);
    });

    return formattedText;
  };

  return (
    <ContentContainer
      sx={{
        backgroundColor: (theme) => theme.palette.primary[900],
        color: (theme) => theme.palette.common.white,
        padding: (theme) => `${theme.spacing(6)} ${theme.spacing(8)} !important`,
        gap: (theme) => theme.spacing(3),
        position: "relative",
        [theme.breakpoints.down("md")]: {
          padding: (theme) => `${theme.spacing(5)}`,
        },
      }}
    >
      <Typography variant="h4" color="inherit">
        {title}
      </Typography>
      {subtitle && (
        <Typography
          color="inherit"
          dangerouslySetInnerHTML={{
            __html: formatText(subtitle, boldWords),
          }}
        />
      )}
      {titlelist && (
        <Typography
          color="inherit"
          dangerouslySetInnerHTML={{
            __html: formatText(titlelist, boldWords),
          }}
        />
      )}
      {listitems ? (
        <ul>
          {listitems?.map((option, i) => (
            <li key={i} className="white-marker-li">
              <Typography
                variant="body2"
                color="inherit"
                dangerouslySetInnerHTML={{
                  __html: formatText(option, boldWords),
                }}
              />
            </li>
          ))}
        </ul>
      ) : null}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          right: 0,
          color: (theme) => theme.palette.primary[500],
          opacity: 0.1,
        }}
      >
        <DoubleQuotesIcon />
      </Box>
      <Box
        sx={{
          position: "absolute",
          bottom: 0,
          left: 0,
          transform: "rotate(180deg)",
          color: (theme) => theme.palette.primary[500],
          opacity: 0.1,
        }}
      >
        <DoubleQuotesIcon />
      </Box>
    </ContentContainer>
  );
}

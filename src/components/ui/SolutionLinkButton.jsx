import ArrowRight from "@assets/icons/arrow_right.svg?react";
import { Button } from "@mui/material";
import { Link } from "react-router-dom";
import styled from "@emotion/styled";

const ArrowRightStyled = styled(ArrowRight)`
  transform: translateX(0);
  transition: transform 0.2s;
`;

const ButtonStyled = styled(Button)`
  justify-content: start;
  background-color: #008fee;
  &:hover .arrow {
    transform: translateX(5px);
  }
  ,
  &:hover {
    background-color: #006cb4;
  }
`;

export default function SolutionLinkButton({ text, to, children, ...props }) {
  return (
    <ButtonStyled
      component={Link}
      to={to}
      variant="secondary"
      color="inherit"
      endIcon={<ArrowRightStyled className="arrow" stroke="inherit" />}
      {...props}
    >
      {text}
      {children}
    </ButtonStyled>
  );
}

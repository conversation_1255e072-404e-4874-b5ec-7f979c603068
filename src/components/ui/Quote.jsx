import DoubleQuotesIcon from "@assets/icons/double_quotes.svg?react";
import { Box, Typography } from "@mui/material";
import { ContentContainer } from "./common.styled";

export default function Quote({ author, text, post, image }) {
  return (
    <ContentContainer
      sx={{
        backgroundColor: (theme) => theme.palette.primary[900],
        color: "secondary.100",
        padding: {
          xs: 4,
          md: 6,
        },
        gap: (theme) => theme.spacing(3),
        position: "relative",
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: {
            xs: "column",
            md: "row",
          },
          gap: 3,
        }}
      >
        <Box>
          <img src={image} height={"160"} width={"160"} alt={author} />
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 3,
          }}
        >
          <Typography
            sx={{
              color: "inherit",
            }}
          >
            {text}
          </Typography>
          <Box display={"flex"} gap={1}>
            <Typography variant="h6" color="inherit">{author},</Typography>
            <Typography color="inherit">{post}</Typography>
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          position: "absolute",
          top: 0,
          right: 0,
          color: (theme) => theme.palette.primary[500],
          opacity: 0.1,
        }}
      >
        <DoubleQuotesIcon />
      </Box>
      <Box
        sx={{
          position: "absolute",
          bottom: 0,
          left: 0,
          transform: "rotate(180deg)",
          color: (theme) => theme.palette.primary[500],
          opacity: 0.1,
        }}
      >
        <DoubleQuotesIcon />
      </Box>
    </ContentContainer>
  );
}

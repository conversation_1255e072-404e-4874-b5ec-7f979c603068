import DoubleQuotesIcon from "@assets/icons/double_quotes.svg?react";
import { Box, Typography, useTheme } from "@mui/material";
import { ContentContainer } from "./common.styled";
import SolutionLinkButton from "./SolutionLinkButton";
import ROUTES from "@/enums/ROUTES";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function BannerQuote({ title, listitems, titlelist, boldWords = [] }) {
  const theme = useTheme();
  const { url } = useRouting();

  const routeButton = {
    PIM: "PIM",
    DAM: "DAM",
    Complétude: "COMPLETUDE",
    Syndication: "SYNDICATION",
    "Portail de marque": "BRAND_PORTAL",
    "HUB Logistique": "HUB",
    "Workflow de validation": "WORKFLOW",
    "Connecteurs Marketplaces": "CONNECTORS",
    "Logistics HUB": "HUB",
    "Marketplace connectors": "CONNECTORS",
    Completeness: "COMPLETUDE",
    "Validation workflow": "WORKFLOW",
    "Brand Portal": "BRAND_PORTAL",
  };

  const formatText = (text, boldWordsArray) => {
    if (!text || !Array.isArray(boldWordsArray) || boldWordsArray.length === 0) return text;

    const escapeRegExp = (string) => {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    };

    let formattedText = text;

    boldWordsArray.forEach((phrase) => {
      const escapedPhrase = escapeRegExp(phrase);
      const regex = new RegExp(escapedPhrase, "g");
      formattedText = formattedText.replace(regex, `<strong>${phrase}</strong>`);
    });

    return formattedText;
  };

  return (
    <ContentContainer
      sx={{
        backgroundColor: (theme) => theme.palette.primary[900],
        color: (theme) => theme.palette.common.white,
        padding: (theme) => `${theme.spacing(6)} ${theme.spacing(8)} !important`,
        gap: (theme) => theme.spacing(3),
        position: "relative",
        [theme.breakpoints.down("md")]: {
          padding: (theme) => `${theme.spacing(5)}`,
        },
      }}
    >
      <Typography variant="h3" color="inherit">
        {title}
      </Typography>

      {titlelist.map((option, i) => (
        <Typography
          key={i}
          variant="body1"
          dangerouslySetInnerHTML={{
            __html: formatText(option, boldWords),
          }}
          sx={{
            color: theme.palette.secondary[200],
          }}
        />
      ))}

      {Array.isArray(listitems) && (
        <Box>
          {listitems.map((item, i) => (
            <Box
              key={i}
              mb={2}
              sx={{
                backgroundColor: theme.palette.primary[800],
                p: 2,
                borderRadius: 3,
                display: "flex",
                flexDirection: { xs: "column", sm: "row" },
                justifyContent: "space-between",
                alignItems: { xs: "flex-start", sm: "center" },
                gap: { xs: 2, sm: 0 },
              }}
            >
              <Typography
                variant="body2"
                color="inherit"
                dangerouslySetInnerHTML={{
                  __html: formatText(item.title, boldWords),
                }}
                sx={{ flex: 1 }}
              />
              <Box sx={{ ml: 2 }}>
                <SolutionLinkButton text={item.button_text} to={url(ROUTES[routeButton[item.button_text]])} />
              </Box>
            </Box>
          ))}
        </Box>
      )}

      <Box
        sx={{
          position: "absolute",
          top: 0,
          right: 0,
          color: (theme) => theme.palette.primary[500],
          opacity: 0.1,
        }}
      >
        <DoubleQuotesIcon />
      </Box>
      <Box
        sx={{
          position: "absolute",
          bottom: 0,
          left: 0,
          transform: "rotate(180deg)",
          color: (theme) => theme.palette.primary[500],
          opacity: 0.1,
        }}
      >
        <DoubleQuotesIcon />
      </Box>
    </ContentContainer>
  );
}

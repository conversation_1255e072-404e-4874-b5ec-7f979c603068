import Share from "@/components/Resources/Share";
import { PagesContainer } from "@/components/ui/common.styled";
import { Box, Breadcrumbs, Chip, Container, Grid, Link, Stack, Typography } from "@mui/material";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import { useTranslation } from "react-i18next";
import { toDate } from "@/utils/date.utils.js";
import BannerQuote from "@/components/ui/BannerQuote.jsx";
import { useData } from "@/contexts/DataProvider";

export default function ResourcesPageTemplate({ breadcrumb, resource }) {
  const { t } = useTranslation();
  const { trans } = useRouting();
  const { reviews } = useData();

  let review = null;
  if (resource.review) {
    review = reviews.find((r) => (r.uuid = resource.review));
  }

  return (
    <PagesContainer sx={{ paddingTop: (theme) => theme.spacing(4) }}>
      <Container
        maxWidth="xl"
        sx={{ display: "flex", flexDirection: "column", gap: { xs: 0, xl: 4 } }}
      >
        <Grid container spacing={{ xs: 4 }}>
          <Grid item xs={12}>
            <Breadcrumbs>
              {breadcrumb.map((item, index) => (
                <Box
                  component={item?.href ? Link : Typography}
                  key={index}
                  underline="hover"
                  color="inherit"
                  href={item?.href}
                >
                  {item.label}
                </Box>
              ))}
            </Breadcrumbs>
          </Grid>
          <Grid item xs={12} xl sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            <Box display={{ xs: "none", xl: "block" }}>
              <Share />
            </Box>
          </Grid>
          <Grid item xs={12} xl={8}>
            <Stack gap={5}>
              <Stack gap={1}>
                <Chip label={t(`resources_categories.${resource.tags[0]}`)} />
                <Typography variant="h2" margin={0}>
                  {trans(resource.title)}
                </Typography>
                <Typography variant="caption">
                  {toDate(resource.updatedAt ? resource.updatedAt : resource.createdAt)}
                </Typography>
              </Stack>
              {resource.image ? (
                <Box>
                  <img
                    src={resource.image}
                    style={{ borderRadius: "16px" }}
                    alt={t("case_studies.gdm.alt_img")}
                    width="100%"
                  />
                </Box>
              ) : null}
              <Typography
                component="div"
                dangerouslySetInnerHTML={{ __html: trans(resource.content) }}
              />
              {review ? (
                <BannerQuote
                  title={trans(review.review)}
                  subtitle={trans(review.job)}
                />
              ) : null}
            </Stack>
          </Grid>
        </Grid>
      </Container>
    </PagesContainer>
  );
}

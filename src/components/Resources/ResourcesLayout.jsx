import { Outlet } from "react-router-dom";
import { PagesContainer } from "@/components/ui/common.styled";
import { Box, Container } from "@mui/material";
import { Discover, News } from "@/components/Layout";
import { useTranslation } from "react-i18next";

export default function ResourcesLayout() {
  const { t } = useTranslation();

  return (
    <PagesContainer>
      <Outlet />
      <Box sx={{ backgroundColor: (theme) => theme.palette.primary[100] }}>
        <Container maxWidth="lg" sx={{ paddingY: 10 }}>
          <News title={t("news_detail.same_news_title")} />
        </Container>
      </Box>
      <Container maxWidth="lg">
        <Discover />
      </Container>
    </PagesContainer>
  );
}

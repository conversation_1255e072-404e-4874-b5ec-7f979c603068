import { useTranslation } from "react-i18next";
import { <PERSON><PERSON>c<PERSON><PERSON>, Chip, Container, Grid, Link, List, ListItem, ListItemButton, Typography } from "@mui/material";
import { PagesContainer } from "../ui/common.styled";
import SEO from "../ui/SEO";
import ROUTES from "@/enums/ROUTES.js";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import { glossary } from "../../data";
import { useEffect, useState } from "react";
import { get } from "@/utils/api.utils";

export default function Glossaire() {
  const { t } = useTranslation();
  const { url, trans } = useRouting();

  const breadcrumb = [
    {
      label: t("common.home"),
      route: ROUTES.HOME,
    },
    {
      label: t("menu.resources.label"),
      route: ROUTES.RESOURCES,
    },
    {
      label: t("resources.glossaire.title"),
    },
  ];

  const getDomId = (uuid) => `glossary-${uuid}`;
  const handleTermClick = (uuid) => {
    const element = document.getElementById(getDomId(uuid));
    if (element) {
      window.scrollTo({
        top: element.getBoundingClientRect().top + window.scrollY - 70,
        behavior: "smooth",
      });
    }
  };

  const [glossary, setGlossary] = useState([]);

  useEffect(() => {
    const loadData = async () => {
      try {
        setGlossary(await get("/assets/contents/glossary.json"));
      } catch (err) {
        console.log(err.message);
      }
    };

    loadData();
  }, []);

  return (
    <>
      <SEO title={t("resources.glossaire.seo.meta_title")} description={t("resources.glossaire.seo.meta_desc")} />
      <PagesContainer sx={{ paddingTop: (theme) => theme.spacing(4) }}>
        <Container
          maxWidth="xl"
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: { xs: 0, xl: 4 },
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Breadcrumbs>
                {breadcrumb.map((item, index) => (
                  <Link
                    key={index}
                    underline="hover"
                    href={item.route ? url(item.route) : null}
                    sx={{
                      color: item.route ? "red" : "blue",
                      textDecoration: "none",
                      "&:hover": {
                        textDecoration: "underline",
                      },
                    }}
                  >
                    {item.label}
                  </Link>
                ))}
              </Breadcrumbs>
              <Typography sx={{ pt: 4 }} variant="h6" gutterBottom>
                {t("resources.glossaire.summary")}
              </Typography>
              <List>
                {glossary?.map((item) => (
                  <ListItem
                    key={item.uuid}
                    sx={{ cursor: "pointer", pb: 0, pt: 0 }}
                    href={`#${getDomId(item.uuid)}`}
                    onClick={(e) => {
                      e.preventDefault();
                      handleTermClick(item.uuid);
                    }}
                  >
                    <ListItemButton disableGutters disableTouchRipple sx={{ pb: 0.5, pt: 0.5 }}>
                      <Typography variant="body1">{trans(item.name)}</Typography>
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </Grid>
            <Grid item xs={12} md={8} sx={{ mt: 7 }}>
              <Chip sx={{ mb: 3 }} label={t("resources.glossaire.title")} color="secondary" />
              <Typography variant="h1">{t("resources.glossaire.title")}</Typography>
              {glossary?.map((item) => (
                <section key={item.uuid} id={getDomId(item.uuid)}>
                  <Typography variant="h3" sx={{ my: 2 }}>
                    {trans(item.name)}
                  </Typography>
                  <Typography variant="body1" dangerouslySetInnerHTML={{ __html: trans(item.description) }} />
                </section>
              ))}
            </Grid>
          </Grid>
        </Container>
      </PagesContainer>
    </>
  );
}

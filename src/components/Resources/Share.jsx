import { Box, Snackbar, Typography } from "@mui/material";
import { Link } from "react-router-dom";
import { LogoContainer } from "@/components/ui/common.styled";
import { useTranslation } from "react-i18next";
import ShareIcon from "@assets/icons/share.svg?react";
import { useState } from "react";
import { useData } from "@/contexts/DataProvider";

export default function Share() {
  const { t } = useTranslation();
  const { socials } = useData();

  const [snack, setSnack] = useState({
    open: false,
    message: "",
  });

  const href = window.location.href;

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
      <Typography variant="h5" sx={{ fontWeight: "bold" }}>
        {t("actions.share")}
      </Typography>
      <Box sx={{ display: "flex", gap: 1 }}>
        {socials.map(social => (
          <Link key={social.uuid} to={social.share ? social.share.replace("%href%", href) : social.url} target="blank">
            <LogoContainer>
              <img src={social.logo} height="100%" width="100%" alt={`${social.name} Logo`} />
            </LogoContainer>
          </Link>
        ))}
        <LogoContainer
          onClick={() => {
            navigator.clipboard.writeText(window.location.href);
            setSnack({ open: true, message: t("actions.link_copied") });
          }}
        >
          <ShareIcon />
        </LogoContainer>
      </Box>
      <Snackbar
        open={snack.open}
        autoHideDuration={5000}
        onClose={() => setSnack({ open: false, message: "" })}
        message={snack.message}
      />
    </Box>
  );
}

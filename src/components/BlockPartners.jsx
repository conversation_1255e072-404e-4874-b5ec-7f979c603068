import { Box } from "@mui/material";
import styled from "@emotion/styled";
import { useData } from "@/contexts/DataProvider";

const BoxStyled = styled(Box)(() => ({
  maxHeight: "50px",
  maxWidth: "150px",
}));

const BoxImg = styled("img")(() => ({
  height: "100%",
  width: "100%",
  objectFit: "contain",
}));

export default function BlockPartners() {
  const { partners } = useData();

  return (
    <Box sx={{ display: "flex", justifyContent: "center", gap: 4 }}>
      {partners?.map((partner) => (
        <BoxStyled key={partner.uuid}>
          <BoxImg src={partner.logo} alt={partner.name} />
        </BoxStyled>
      ))}
    </Box>
  );
}

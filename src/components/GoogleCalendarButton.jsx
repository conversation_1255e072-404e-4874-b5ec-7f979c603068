import { useEffect, useRef } from "react";
import { Helmet } from "react-helmet";
import { useTranslation } from "react-i18next";

export default function GoogleCalendarButton() {
  const buttonRef = useRef(null);
  const { t } = useTranslation();

  useEffect(() => {
    const scriptJS = document.createElement("script");
    scriptJS.src = "https://calendar.google.com/calendar/scheduling-button-script.js";
    scriptJS.async = true;
    document.body.appendChild(scriptJS);

    const handleScriptLoad = () => {
      if (window.calendar?.schedulingButton) {
        window.calendar.schedulingButton.load({
          url: "https://calendar.google.com/calendar/appointments/schedules/AcZssZ2bgfzEBpi67mqA5yClLZpOtvZHSVR2Cvkl6tlSkA2hsSv3D0VCMJhaSN1AKHJEjYGB3qAr3VsB?gv=true",
          color: "#039BE5",
          label: t("ask_demo.form.calendar.book_appointment"),
          target: buttonRef.current,
        });
      }
    };

    scriptJS.addEventListener("load", handleScriptLoad);

    return () => {
      scriptJS.removeEventListener("load", handleScriptLoad);
      if (document.body.contains(scriptJS)) {
        document.body.removeChild(scriptJS);
      }
    };
  }, [t]);

  return (
    <>
      <Helmet>
        <link 
          href="https://calendar.google.com/calendar/scheduling-button-script.css" 
          rel="stylesheet" 
        />
      </Helmet>
      <div ref={buttonRef} />
    </>
  );
}
import FeatureCard from "@/components/ui/cards/FeatureCard/FeatureCard";
import { Box, Button, Chip, Grid, Typography, useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";
import { Link, useNavigate } from "react-router-dom";
import ROUTES from "../../enums/ROUTES.js";
import { colorWords } from "@/utils/string.utils";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import { useData } from "@/contexts/DataProvider.jsx";

export default function News() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { trans } = useRouting();
  const theme = useTheme();
  const { url } = useRouting();
  const { resources } = useData();

  return (
    <Box
      component="section"
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: 4,
      }}
    >
      <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 3 }}>
        <Chip label={t("layout.footer.news.tag")} color="primary" />
        <Typography
          variant="h2"
          dangerouslySetInnerHTML={{
            __html: colorWords(
              t("layout.footer.news.title"),
              t("layout.footer.news.title_colored_words").split(" "),
              theme.palette.primary.main
            ),
          }}
        />
      </Box>
      <Grid container spacing={2}>
        {resources.slice(0, 3).map((resource) => (
          <Grid item xs={12} sm={4} key={resource.uuid}>
            <FeatureCard
              image={resource.image}
              tags={resource.tags}
              title={trans(resource.title)}
              date={resource.updatedAt ?? resource.createdAt}
              onClick={() => navigate(url(ROUTES.RESOURCES_SLUG, { slug: resource.slug }))}
            />
          </Grid>
        ))}
      </Grid>
      <Button variant="contained" color="primary" component={Link} to={url(ROUTES.RESOURCES)}>
        {t("layout.footer.news.btn_see_all")}
      </Button>
    </Box>
  );
}

import { Box, Grid } from "@mui/material";
import ReviewCard from "@/components/ui/cards/ReviewCard";
import Swiper from "react-id-swiper";
import { useRef } from "react";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import { useData } from "@/contexts/DataProvider";

export default function Reviews() {
  const { trans } = useRouting();
  const swiperRef = useRef(null);
  const { reviews } = useData();

  const params = {
    slidesPerView: 1,
    spaceBetween: 30,
    breakpoints: {
      600: {
        slidesPerView: 2,
      },
    },
  };

  return (
    <Box component={"section"} p={2} sx={{ overflowX: "hidden" }}>
      <Swiper container {...params} ref={swiperRef}>
        {reviews.map((review) => (
          <Grid key={review.uuid} item xs={12}>
            <ReviewCard
              image={review.image}
              alt={trans(review.job)}
              text={trans(review.review)}
              author={{
                name: review.author,
                post: trans(review.job),
              }}
            />
          </Grid>
        ))}
      </Swiper>
    </Box>
  );
}

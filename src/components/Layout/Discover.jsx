import DiscoverIllustration from "@assets/home/<USER>";
import SinfinLogo from "@assets/sinfin-logo-circle-dotted.png";
import { Box, Button, Grid, Typography, useTheme } from "@mui/material";
import { ContentContainer } from "@/components/ui/common.styled";
import ROUTES from "../../enums/ROUTES.js";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function Discover() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { url } = useRouting();

  return (
    <ContentContainer
      sx={{
        backgroundColor: (theme) => theme.palette.primary[900],
        padding: (theme) => theme.spacing(4),
        color: (theme) => theme.palette.common.white,
        [theme.breakpoints.up("md")]: {
          padding: (theme) => `${theme.spacing(6)} ${theme.spacing(7)}`,
        },
      }}
      component="section"
    >
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: (theme) => theme.spacing(2),
              color: "common.white",
            }}
          >
            <Typography variant="h1" color="inherit">
              {t("layout.footer.discover.title")}
            </Typography>
            <Typography color="secondary.200">
              {t("layout.footer.discover.subtitle")}
            </Typography>
            <Button
              sx={{
                width: "max-content",
              }}
              component={Link}
              to={url(ROUTES.ASK_DEMO)}
            >
              {t("common.ask_demo")}
            </Button>
          </Box>
        </Grid>
        <Grid item xs={12} sm={6}>
          <Box
            sx={{
              maxHeight: 278,
              maxWidth: 477,
              position: "relative",
            }}
          >
            <img
              src={DiscoverIllustration}
              alt={"Discover illustration"}
              height={"100%"}
              width={"100%"}
              aria-hidden={true}
            />
            <Box
              sx={{
                position: "absolute",
                right: -10,
                zIndex:1,
                top: -40,
                height: 110,
                [theme.breakpoints.up("sm")]: {
                  height: 134,
                  top: -20,
                  right: -20,
                },
                [theme.breakpoints.up("md")]: {
                  height: 134,
                  top: -20,
                  right: -40,
                },
              }}
            >
              <img
                src={SinfinLogo}
                alt={"Sinfin logo"}
                height={"100%"}
                width={"100%"}
                style={{ objectFit: "contain" }}
              />
            </Box>
          </Box>
        </Grid>
      </Grid>
    </ContentContainer>
  );
}

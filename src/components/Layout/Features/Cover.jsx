import { ContentContainer } from "@/components/ui/common.styled";
import { Typography } from "@mui/material";
import { Link } from "react-router-dom";

export default function Cover({ title, image, href }) {
  return (
    <ContentContainer component={href ? Link : "div"} to={href} sx={{
      backgroundImage: `url(${image})`,
      backgroundSize: "cover",
      backgroundPosition: "center",
      height: 180,
      width: "100%",
      position: "relative",
      padding: 0,
    }}>
      <Typography variant="h5" sx={{
        position: "absolute",
        bottom: 0,
        padding: (theme) => theme.spacing(2),
        borderRadius: (theme) => theme.spacing(3),
        width: "-webkit-fill-available",
        color: (theme) => theme.palette.common.white,
        background:
          "linear-gradient(180deg, rgba(127, 127, 127, 0) 0%, rgba(15, 18, 27, 0.15) 100%);",
      }}>
        {title}
      </Typography>
    </ContentContainer>
  );
}

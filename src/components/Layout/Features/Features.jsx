import { Box, Chip, Container, Grid, Typography, useTheme } from "@mui/material";
import Dam from "@assets/home/<USER>";
import Pim from "@assets/home/<USER>";
import Hub from "@assets/home/<USER>";
import Centralize from "@assets/home/<USER>";
import Publish from "@assets/home/<USER>";
import Optimize from "@assets/home/<USER>";
import Workflow from "@assets/home/<USER>";
import { colorWords } from "@/utils/string.utils";
import { useTranslation } from "react-i18next";
import ROUTES from "../../../enums/ROUTES.js";
import Cover from "./Cover";
import Swiper from "react-id-swiper";
import { Autoplay } from "swiper";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function Features() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { url } = useRouting();

  const data = [
  //   {
  //   label: t("index.features.centralize"),
  //   image: Centralize,
  //   href: url(ROUTES.CENTRALIZE),
  // }, {
  //   label: t("index.features.diffuse"),
  //   image: Publish,
  //   href: url(ROUTES.DISTRIBUTE),
  // }, {
  //   label: t("index.features.optimize"),
  //   image: Optimize,
  //   href: url(ROUTES.OPTIMIZE),
  // }
   {
    label: "PIM",
    image: Pim,
    href: url(ROUTES.PIM),
  }, {
    label: "DAM",
    image: Dam,
    href: url(ROUTES.DAM),
  }, {
    label: "Hub / OMS",
    image: Hub,
    href: url(ROUTES.HUB),
  }, {
    label: "Workflow",
    image: Workflow,
    href: url(ROUTES.WORKFLOW),
  }];

  const params = {
    modules: [Autoplay],
    autoplay: {
      delay: 4000,
    },
    slidesPerView: 1.2,
    spaceBetween: 60,
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    breakpoints: {
      1024: {
        slidesPerView: 3.2,

      },
      600: {
        slidesPerView: 2.2,

      },
    },
  };

  return (
    <Box
      component="section"
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: (theme) => theme.spacing(6),
        color: (theme) => theme.palette.primary[900],
      }}
    >
      <Container
        maxWidth="2xl"
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: (theme) => theme.spacing(3),
        }}
      >
        <Chip label={t("index.features.tag")} color="primary" />
        <Typography
          dangerouslySetInnerHTML={{
            __html: colorWords(
              t("index.features.title"),
              t("index.features.title_colored_words").split(" "),
              theme.palette.primary.main,
            ),
          }}
          variant="h2"
          sx={{
            maxWidth: "600px",
          }}
        />
      </Container>
      <Container maxWidth="2xl" sx={{ overflowX: "hidden" }}>
        <Swiper {...params}>
          {data.map((item, i) => (
            <Grid item xs={12} xl={3} key={i}>
              <Cover title={item.label} image={item.image} href={item.href} />
            </Grid>
          ))}
        </Swiper>
      </Container>
    </Box>
  );
}

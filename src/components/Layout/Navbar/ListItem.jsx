import ChevronDownIcon from "@assets/icons/chevron-down.svg?react";
import ChevronUpIcon from "@assets/icons/chevron-up.svg?react";
import { Collapse, List, ListItemButton, ListItemIcon, Typography, useTheme } from "@mui/material";
import { useState } from "react";
import { Link } from "react-router-dom";

export default function ListItem({ startIcon, endIcon, text, children, to, onClick }) {
  const [open, setOpen] = useState(false);
  const theme = useTheme();

  return (
    <>
      <ListItemButton
        onClick={onClick ? onClick : children ? () => setOpen(!open) : null}
        sx={{
          color: open ? "primary.600" : "text.primary",
          display: "flex",
          alignItems: "center",
          gap: 1,
        }}
        component={Link}
        to={to}
      >
        {startIcon && <ListItemIcon>{startIcon}</ListItemIcon>}
        {text && <Typography>{text}</Typography>}
        {children &&
          (open ? (
            <ChevronUpIcon stroke={theme.palette.primary[600]} />
          ) : (
            <ChevronDownIcon stroke={theme.palette.text.body} />
          ))}
        {endIcon && <ListItemIcon>{endIcon}</ListItemIcon>}
      </ListItemButton>
      {children && (
        <Collapse in={open} timeout="auto" unmountOnExit>
          <List
            component="div"
            disablePadding
            sx={{
              fontWeight: "bold",
            }}
          >
            {children}
          </List>
        </Collapse>
      )}
    </>
  );
}

import { Box, List, ListItem, Typography } from "@mui/material";
import LinkButton from "@/components/ui/LinkButton";
import { Link } from "react-router-dom";

export default function SubNavLink({ label, to, children }) {
  return (
    <Box
      sx={{
        "a": {
          transition: (theme) => theme.transitions.create("color"),
        },
        "&:hover": {
          "> a": {
            color: "primary.main",
          },
        },
      }}
    >
      <Typography variant="h4" color="secondary.300" component={Link} to={to}>
        {label}
      </Typography>
      <List>
        {children.map((c, i) => (
          <ListItem
            key={i}
            sx={{
              marginLeft: 1,
            }}
          >
            <LinkButton
              color="inherit"
              sx={{
                padding: 0,
              }}
              to={c.to}
            >
              {c.label}
            </LinkButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );
}

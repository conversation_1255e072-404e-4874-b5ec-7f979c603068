import { Box, Grid, Typography, Container } from "@mui/material";
import { useNavigate } from "react-router-dom";

export default function SubNav({ label, description, children }) {
  const navigate = useNavigate();
  const baseIconStyle = {
    width: 40,
    height: 40,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "primary.200",
    borderRadius: "8px",
  };

  const renderIcon = (icon, label, index) =>
    icon ? (
      <Box sx={baseIconStyle}>
        <img src={icon} alt={label} width="auto" height="auto" style={{ objectFit: "contain" }} />
      </Box>
    ) : (
      <Box sx={{ ...baseIconStyle, color: "primary.main", fontSize: "18px" }}>{String.fromCharCode(65 + index)}</Box>
    );

  const handleNavItemClick = (to, event) => {
    event.preventDefault();
    event.stopPropagation();
    setTimeout(() => {
      navigate(to);
      document.querySelector('.link[aria-expanded="true"]')?.click();
    }, 10);
  };

  const renderNavItem = (child, index) => (
    <Box
      key={index}
      component="a"
      href={child.to}
      onClick={(e) => handleNavItemClick(child.to, e)}
      sx={{
        display: "flex",
        mb: 5,
        p: 2,
        height: "3em",
        width: "90%",
        transition: "all 0.15s ease-out",
        ":hover": {
          backgroundColor: "primary.100",
          borderRadius: "8px"
        }
      }}
    >
      <Box sx={{ mr: 2 }}>{renderIcon(child.icon, child.label, index)}</Box>
      <Box>
        <Typography variant="bold">{child.label}</Typography>
        {child.description && <Typography variant="body2">{child.description}</Typography>}
      </Box>
    </Box>
  );

  const midPoint = Math.ceil(children.length / 2);

  return (
    <Container
      maxWidth="xl"
      sx={{ margin: "0 auto", padding: 0, display: { xs: "none", lg: "block" } }}
      className="popper-content"
    >
      <Grid container spacing={4} sx={{ position: "relative" }} className="popper-content">
        <Box
          sx={{
            position: "absolute",
            left: "25%",
            top: 0,
            bottom: 0,
            width: "1px",
            backgroundColor: "grey.200",
            zIndex: 1,
          }}
          className="popper-content"
        />
        <Grid item xs={12} md={3} className="popper-content">
          <Box sx={{ pr: 4, height: "100%" }} className="popper-content">
            <Typography variant="h1">{label}</Typography>
            {description && <Typography variant="body2">{description}</Typography>}
          </Box>
        </Grid>
        <Grid item xs={12} md={9} className="popper-content">
          <Grid container className="popper-content">
            <Grid item xs={12} md={6} className="popper-content">
              {children.slice(0, midPoint).map(renderNavItem)}
            </Grid>
            <Grid item xs={12} md={6} className="popper-content">
              {children.slice(midPoint).map((item, index) => renderNavItem(item, index + midPoint))}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Container>
  );
}

import React, { Fragment } from "react";
import LinkButton from "@/components/ui/LinkButton";
import { Box, List, ListItem as MuiListItem, Toolbar } from "@mui/material";
import ListItem from "./ListItem";
import config from "./navbar.config";
import { useTranslation } from "react-i18next";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import { layoutMarginTop } from "../Layout";

export default function MobileMenu({ onClose }) {
  const { t } = useTranslation();
  const { url } = useRouting();

  return (
    <Box
      height="100%"
      width="100%"
      sx={{
        backgroundColor: (theme) => theme.palette.secondary[25],
        overflowY: "auto",
        mt: layoutMarginTop,
      }}
    >
      <List>
        {config(t, url).map((item, index) => (
          <ListItem key={`item-${index}`} text={item.label} to={item.to} onClick={item.to ? onClose : null}>
            {item.children?.map((child, childIndex) => (
              <Fragment key={`child-${index}-${childIndex}`}>
                <MuiListItem key={`child-item-${index}-${childIndex}`}>
                  <LinkButton color="inherit" to={child.to} text={child.label} onClick={onClose} />
                </MuiListItem>
                {child.children?.map((subChild, subChildIndex) => (
                  <MuiListItem key={`sub-child-item-${index}-${childIndex}-${subChildIndex}`}>
                    <LinkButton color="inherit" to={subChild.to} text={subChild.label} onClick={onClose} />
                  </MuiListItem>
                ))}
              </Fragment>
            ))}
          </ListItem>
        ))}
      </List>
    </Box>
  );
}

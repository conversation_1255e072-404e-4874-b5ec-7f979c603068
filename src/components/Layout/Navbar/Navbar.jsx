import React, { useState } from "react";
import HamburgerIcon from "@assets/icons/hamburger.svg?react";
import GlobeIcon from "@assets/icons/globe.svg?react";
import SinfinLogo from "@assets/sinfin.svg";
import {
  AppBar,
  Box,
  Button,
  Container,
  IconButton,
  Menu,
  MenuItem,
  SwipeableDrawer,
  Toolbar,
  Typography,
  useTheme,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import MobileMenu from "./MobileMenu";
import NavLinksGroup from "./NavLinksGroup";
import ROUTES from "../../../enums/ROUTES.js";
import { Link, useNavigate } from "react-router-dom";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import LANGS from "../../../enums/LANGS.js";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function Navbar() {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const { url } = useRouting();

  const [anchorEl, setAnchorEl] = useState(null);
  const [menuOpen, setMenuOpen] = useState(false);

  const open = Boolean(anchorEl);

  const handleClick = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const changeLanguageAndCloseMenu = async (lang) => {
    await i18n.changeLanguage(lang, () => {
      navigate(`/${lang}`);
      handleClose();
    });
  };

  return (
    <nav>
      <AppBar
        elevation={0}
        sx={{
          backgroundColor: theme.palette.common.white,
          zIndex: (theme) => theme.zIndex.drawer + 1,
          borderBottom: (theme) => `solid 1px ${theme.palette.grey[300]}`,
        }}
      >
        {/* <Link
          to={url(ROUTES.LANDING_LEAD_GEN, { lang: i18n.language })}
          style={{
            backgroundColor: "#98fb98",
            color: "black",
            textAlign: "center",
            padding: "5px",
            borderBottom: `1px solid ${theme.palette.grey[300]}`,
            cursor: "pointer",
          }}
        >
          <Typography fontSize="14px">
            <strong>{t("resources.webinaire.banner_title")}</strong> {t("resources.webinaire.banner_content")}
          </Typography>
        </Link> */}
        <Toolbar
          sx={{
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Container
            maxWidth="xl"
            sx={{
              display: "flex",
              alignItems: "center",
              gap: theme.spacing(4),
              justifyContent: "space-between",
              padding: 0,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: theme.spacing(4),
              }}
            >
              <Link to={url(ROUTES.HOME, { lang: i18n.language })} title="Home">
                <img src={SinfinLogo} height={20} alt="Sinfin logo" aria-hidden={true} />
              </Link>
              <Box
                sx={{
                  [theme.breakpoints.down("lg")]: {
                    display: "none",
                  },
                }}
              >
                <NavLinksGroup />
              </Box>
            </Box>
            <Box
              sx={{
                display: { sm: "flex" },
                gap: theme.spacing(2),
              }}
            >
              <Button
                aria-controls={open ? "demo-customized-menu" : undefined}
                aria-haspopup="true"
                aria-expanded={open ? "true" : undefined}
                disableElevation
                onClick={handleClick}
                endIcon={<KeyboardArrowDownIcon />}
                variant="text"
                sx={{
                  color: theme.palette.secondary[700],
                  "&:hover": {
                    color: theme.palette.secondary[700] + " !important",
                  },
                }}
              >
                <GlobeIcon />
                <Typography variant="body2" pl={1} color={"secondary.700"}>
                  {Object.keys(LANGS).find((key) => LANGS[key] === i18n.language)}
                </Typography>
              </Button>
              <Menu
                id="demo-customized-menu"
                slotProps={{
                  paper: {
                    square: true,
                  },
                }}
                MenuListProps={{
                  "aria-labelledby": "demo-customized-button",
                  style: {
                    border: "1px solid #e0e0e0",
                    padding: 0,
                  },
                }}
                anchorEl={anchorEl}
                disableScrollLock
                open={open}
                onClose={handleClose}
              >
                {Object.values(LANGS).map((language) => (
                  <MenuItem
                    key={language}
                    disabled={i18n.language === language}
                    selected={i18n.language === language}
                    onClick={() => changeLanguageAndCloseMenu(language)}
                  >
                    {t(`lang.${language}`)}
                  </MenuItem>
                ))}
              </Menu>
              <Button
                sx={{ display: { xs: "none", sm: "flex" } }}
                variant="contained"
                color="primary"
                component={Link}
                to={url(ROUTES.ASK_DEMO)}
              >
                {t("common.ask_demo")}
              </Button>
            </Box>
          </Container>
          <IconButton
            edge="end"
            color="black"
            aria-label="menu"
            onClick={() => setMenuOpen(!menuOpen)}
            sx={{
              display: { xs: "block", lg: "none" },
            }}
          >
            <HamburgerIcon />
          </IconButton>
        </Toolbar>
        <SwipeableDrawer
          anchor={"right"}
          open={menuOpen}
          onClose={() => setMenuOpen(false)}
          onOpen={() => setMenuOpen(true)}
          disableSwipeToOpen
          sx={{
            flexShrink: 0,
            width: "100%",
            [`& .MuiDrawer-paper`]: { width: "100%", boxSizing: "border-box" },
            [theme.breakpoints.up("lg")]: {
              display: "none",
            },
          }}
        >
          <MobileMenu onClose={() => setMenuOpen(false)} />
        </SwipeableDrawer>
      </AppBar>
    </nav>
  );
}

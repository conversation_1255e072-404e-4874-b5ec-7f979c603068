import { Box, Container } from "@mui/material";
import NavLink from "./NavLink";
import SubNav from "./SubNav";
import config from "./navbar.config";
import { useTranslation } from "react-i18next";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function NavLinksGroup() {
  const { t } = useTranslation();
  const { url } = useRouting();

  return (
    <Container maxWidth="xl">
      <Box display={"flex"} gap={4} component="nav" sx={{ position: "static" }}>
        {config(t, url).map((item, index) => (
          <NavLink key={index} text={item.label} to={item.to}>
            {item.children && <SubNav {...item} label={item.label} description={item.description} />}
          </NavLink>
        ))}
      </Box>
    </Container>
  );
}

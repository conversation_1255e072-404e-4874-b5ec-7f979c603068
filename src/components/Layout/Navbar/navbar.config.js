import ROUTES from "../../../enums/ROUTES.js";

export default function (t, url) {
  return [
    {
      label: t("menu.solutions.label"),
      description: t("menu.solutions.text"),
      children: [
        {
          label: t("menu.solutions.children.ecommerce"),
          to: url(ROUTES.ECOMMERCE),
          icon: "/assets/icons/distrib_auto.svg",
          description: t("menu.solutions.descriptions.ecommerce"),
        },
        {
          label: t("menu.solutions.children.marketplaces"),
          to: url(ROUTES.MARKETPLACES),
          icon: "/assets/icons/marketplaces.svg",
          description: t("menu.solutions.descriptions.marketplaces"),
        },
        {
          label: t("menu.solutions.children.international"),
          to: url(ROUTES.INTERNATIONAL),
          icon: "/assets/icons/location.svg",
          description: t("menu.solutions.descriptions.international"),
        },
        {
          label: t("menu.solutions.children.replatforming"),
          to: url(ROUTES.REPLATFORMING),
          icon: "/assets/icons/replatforming.svg",
          description: t("menu.solutions.descriptions.replatforming"),
        },
        {
          label: t("menu.solutions.children.retail"),
          to: url(ROUTES.RETAIL),
          icon: "/assets/icons/retail.svg",
          description: t("menu.solutions.descriptions.retail"),
        },
        {
          label: t("menu.solutions.children.industry"),
          to: url(ROUTES.INDUSTRY),
          icon: "/assets/icons/industry.svg",
          description: t("menu.solutions.descriptions.industry"),
        },
        {
          label: t("menu.solutions.children.agrifood"),
          to: url(ROUTES.AGRIFOOD),
          icon: "/assets/icons/solution_agrifood.svg",
          description: t("menu.solutions.descriptions.agrifood"),
        },

        {
          label: t("menu.solutions.children.luxe"),
          to: url(ROUTES.LUXE),
          icon: "/assets/icons/luxe.svg",
          description: t("menu.solutions.descriptions.luxe"),
        },
      ],
    },
    {
      label: t("menu.products.label"),
      description: t("menu.products.text"),
      children: [
        {
          label: t("menu.products.children.pim"),
          to: url(ROUTES.PIM),
          icon: "/assets/icons/product_sheet.svg",
          description: t("menu.products.descriptions.pim"),
        },
        {
          label: t("menu.products.children.dam"),
          to: url(ROUTES.DAM),
          icon: "/assets/icons/dam.svg",
          description: t("menu.products.descriptions.dam"),
        },
        {
          label: t("menu.products.children.hub"),
          to: url(ROUTES.HUB),
          icon: "/assets/icons/grouped_orders.svg",
          description: t("menu.products.descriptions.hub"),
        },

        {
          label: t("menu.products.children.syndication"),
          to: url(ROUTES.SYNDICATION),
          icon: "/assets/icons/distrib_auto.svg",
          description: t("menu.products.descriptions.syndication"),
        },
        {
          label: t("menu.products.children.completude"),
          to: url(ROUTES.COMPLETUDE),
          icon: "/assets/icons/completude.svg",
          description: t("menu.products.descriptions.completude"),
        },
        {
          label: t("menu.products.children.location"),
          to: url(ROUTES.LOCATION),
          icon: "/assets/icons/location.svg",
          description: t("menu.products.descriptions.location"),
        },
        {
          label: t("menu.products.children.connectors"),
          to: url(ROUTES.CONNECTORS),
          icon: "/assets/icons/connectors.svg",
          description: t("menu.products.descriptions.connectors"),
        },
        {
          label: t("menu.products.children.workflow"),
          to: url(ROUTES.WORKFLOW),
          icon: "/assets/icons/workflow.svg",
          description: t("menu.products.descriptions.workflow"),
        },
        {
          label: t("menu.products.children.brand_portal"),
          to: url(ROUTES.BRAND_PORTAL),
          icon: "/assets/icons/brandportal.svg",
          description: t("menu.products.descriptions.brand_portal"),
        },
      ],
    },
    {
      label: t("menu.price.label"),
      to: url(ROUTES.PRICES),
    },
    {
      label: t("menu.resources.label"),
      to: url(ROUTES.RESOURCES),
    },
    {
      label: t("menu.company.label"),
      to: url(ROUTES.ABOUT_US),
    },
  ];
}

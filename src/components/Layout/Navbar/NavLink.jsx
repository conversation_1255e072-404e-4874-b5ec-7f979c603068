import ChevronUpIcon from "@assets/icons/chevron-up.svg?react";
import { Box, Collapse, Typography, useTheme } from "@mui/material";
import { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";

export default function NavLink({ text, button, children, to }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const theme = useTheme();
  const navLinkRef = useRef(null);
  const popperRef = useRef(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    event.preventDefault();
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  useEffect(() => {
    const handleClickOutside = ({ target }) =>
      open &&
      !target.closest(".popper-content") &&
      navLinkRef.current &&
      !navLinkRef.current.contains(target) &&
      setAnchorEl(null);
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [open]);

  const linkStyles = {
    color: open ? "primary.600" : "text.body",
    fontWeight: 500,
    display: "flex",
    alignItems: "center",
    gap: 1,
    "&:hover": { color: "primary.600" },
    cursor: "pointer",
    textDecoration: "none",
  };

  return (
    <div ref={navLinkRef}>
      {button ? (
        <div onClick={handleClick}>{button}</div>
      ) : (
        <Typography
          component={to && !children ? Link : "p"}
          to={to && !children ? to : undefined}
          className="link"
          onClick={children ? handleClick : undefined}
          aria-controls={open ? "basic-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={open}
          sx={linkStyles}
        >
          {text}

          {children && (
            <ChevronUpIcon
              style={{ transform: anchorEl ? "rotate(0deg)" : "rotate(180deg)", transition: "transform 0.3s" }}
              stroke={open ? theme.palette.primary[600] : theme.palette.text.body}
            />
          )}
        </Typography>
      )}
      {children && (
        <Collapse
          in={open}
          collapsedSize={0}
          sx={{
            position: "absolute",
            top: "100%",
            left: 0,
            width: "100%",
            zIndex: 9999,
          }}
        >
          <Box
            ref={popperRef}
            sx={{ margin: "0 auto", pt: 8, px: 0, pb: 0, bgcolor: "#fff", boxShadow: (t) => t.shadows[6] }}
            className="popper-content"
          >
            {children}
          </Box>
        </Collapse>
      )}
    </div>
  );
}

import { Outlet, useNavigate, useParams } from "react-router-dom";
import Navbar from "./Navbar/Navbar";
import LayoutFooter from "./Footer/LayoutFooter";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import LANGS from "@/enums/LANGS.js";
import { Box } from "@mui/material";

export const layoutMarginTop = "65px";

export default function Layout() {
  const { lang } = useParams();
  const { i18n } = useTranslation();
  const navigate = useNavigate();

  useEffect(() => {
    if (lang !== i18n.language) {
      if (Object.values(LANGS).includes(lang)) {
        void i18n.changeLanguage(lang);
      } else {
        navigate("/");
      }
    }
  }, [lang]);

  return (
    <div>
      <Navbar />
      <Box sx={{ mt: layoutMarginTop }}>
        <Outlet />
      </Box>
      <LayoutFooter />
    </div>
  );
}

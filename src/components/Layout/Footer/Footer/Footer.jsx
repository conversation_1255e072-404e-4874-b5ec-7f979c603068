import MailIcon from "@assets/icons/mail.svg?react";
import Sin<PERSON>Logo from "@assets/sinfin.svg";
import {
  Box,
  Button,
  Divider,
  Grid,
  InputAdornment, TextField,
  Typography,
  useTheme
} from "@mui/material";
import { useState } from "react";
import FollowUs from "./FollowUs";
import FooterLinks from "./FooterLinks";
import ROUTES from "../../../../enums/ROUTES.js";
import { Link } from "react-router-dom";
import { sendToSelssy } from "@/utils/api.utils.js";
import { useTranslation } from "react-i18next";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import ChatCircle from "@assets/icons/chat_circle.svg?react";
import { SELSSY_NEWSLETTER_VALUE, SELSSY_SOURCE_ID } from "@/env.js";
import Toast from "@/components/Toast";

export default function Footer() {
  const { t } = useTranslation();
  const theme = useTheme();
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snack, setSnack] = useState({
    open: false,
    message: "",
    type: "success",
  });
  const { url } = useRouting();

  const date = new Date();
  let year = date.getFullYear();

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
  };

  const handleFormSubmit = async (e) => {
    try {
      e.preventDefault();
      setIsSubmitting(true);

      const response = await sendToSelssy({
        email,
        sourceId: SELSSY_SOURCE_ID,
        sourceValue: SELSSY_NEWSLETTER_VALUE,
      });

      const data = await response.json();
      setIsSubmitting(false);

      if (response.ok) {
        setEmail("");
        setSnack({
          open: true,
          message: t("common.ask_send"),
          type: "success",
        });
      } else {
        setSnack({
          open: true,
          message: data?.violations?.map((e) => e.message).join(", "),
          type: "error",
        });
      }
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <Box>
      <Grid container spacing={2} gap={2} justifyContent="space-between">
        <Grid
          item
          xs={12}
          xl={4}
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 2,
          }}
        >
          <Box>
            <img
              src={SinfinLogo}
              height={24}
              alt="Sinfin logo"
              aria-hidden={true}
            />
          </Box>
          <Typography>{t("layout.footer.footer.text")}</Typography>
          <Box display={"flex"} gap={1} alignItems={"center"}>
            <ChatCircle />
            <Typography
              component={Link}
              to={url(ROUTES.CONTACT)}
              variant="body1"
              sx={{
                fontWeight: "bold",
              }}
            >
              {t("common.contact_us")}
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={12} xl={7}>
          <FooterLinks />
        </Grid>
        <Grid item xs={12} xl={5}>
          <FollowUs />
        </Grid>
        <Grid item xs={12} xl={6}>
          <Box display="flex" flexDirection="column" gap={1}>
            <Typography
              sx={{
                fontWeight: "bold",
              }}
            >
              {t("layout.footer.footer.newsletter")}
            </Typography>
            <Box
              component="form"
              onSubmit={handleFormSubmit}
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 1,
                [theme.breakpoints.up("md")]: {
                  flexDirection: "row",
                },
              }}
            >
              <TextField
                type="email"
                variant="outlined"
                placeholder="Email"
                size="small"
                fullWidth
                required
                value={email}
                onChange={handleEmailChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <MailIcon />
                    </InputAdornment>
                  ),
                }}
              />
              <Button
                type="submit"
                sx={{
                  [theme.breakpoints.down("md")]: {
                    width: "100%",
                  },
                }}
                disabled={isSubmitting}
              >
                {t("layout.footer.footer.btn_subscribe")}
              </Button>
            </Box>
          </Box>
        </Grid>
        <Grid item xs={12}>
          <Divider />
        </Grid>
        <Grid
          item
          xs={12}
          sx={{
            display: "flex",
            flexDirection: "column",
            padding: 4,
            gap: 2,
            [theme.breakpoints.up("xl")]: {
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            },
          }}
        >
          <Typography variant="body2">
            © {year} SINFIN. {t("layout.footer.footer.rights")}.
          </Typography>
          <Box
            sx={{
              display: "flex",
              gap: 2,
            }}
          >
            <Typography
              component={Link}
              variant="body2"
              to={url(ROUTES.LEGAL_NOTICE)}
            >
              {t("layout.footer.footer.legal")}
            </Typography>
            <Typography
              component={Link}
              variant="body2"
              to={url(ROUTES.PRIVACY)}
            >
              {t("layout.footer.footer.privacy")}
            </Typography>
          </Box>
        </Grid>
      </Grid>
      <Toast
        open={snack.open}
        close={() => setSnack({ open: false, message: "" })}
        message={snack.message}
        type={snack.type}
      />
    </Box>
  );
}

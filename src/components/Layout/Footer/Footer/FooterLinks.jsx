import { Grid, List, ListItem, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import ROUTES from "../../../../enums/ROUTES.js";
import { Link } from "react-router-dom";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function FooterLinks() {
  const { t } = useTranslation();
  const { url } = useRouting();

  const columns = [
    {
      title: t("menu.solutions.label"),
      links: [
        {
          title: t("menu.solutions.children.ecommerce"),
          to: url(ROUTES.ECOMMERCE),
        },
        {
          title: t("menu.solutions.children.marketplaces"),
          to: url(ROUTES.MARKETPLACES),
        },
        {
          title: t("menu.solutions.children.international"),
          to: url(ROUTES.INTERNATIONAL),
        },
        {
          title: t("menu.solutions.children.replatforming"),
          to: url(ROUTES.REPLATFORMING),
        },
        {
          title: t("menu.solutions.children.retail"),
          to: url(ROUTES.RETAIL),
        },
        {
          title: t("menu.solutions.children.industry"),
          to: url(ROUTES.INDUSTRY),
        },
        {
          title: t("menu.solutions.children.agrifood"),
          to: url(ROUTES.AGRIFOOD),
        },
        {
          title: t("menu.solutions.children.luxe"),
          to: url(ROUTES.LUXE),
        },
      ],
    },
    {
      title: t("menu.products.label"),
      links: [
        {
          title: t("menu.products.children.pim"),
          to: url(ROUTES.PIM),
        },
        {
          title: t("menu.products.children.dam"),
          to: url(ROUTES.DAM),
        },
        {
          title: t("menu.products.children.hub"),
          to: url(ROUTES.HUB),
        },
        {
          title: t("menu.products.children.syndication"),
          to: url(ROUTES.SYNDICATION),
        },
        {
          title: t("menu.products.children.completude"),
          to: url(ROUTES.COMPLETUDE),
        },
        {
          title: t("menu.products.children.location"),
          to: url(ROUTES.LOCATION),
        },
        {
          title: t("menu.products.children.connectors"),
          to: url(ROUTES.CONNECTORS),
        },
        {
          title: t("menu.products.children.workflow"),
          to: url(ROUTES.WORKFLOW),
        },
        {
          title: t("menu.products.children.brand_portal"),
          to: url(ROUTES.BRAND_PORTAL),
        },
      ],
    },
    {
      title: t("menu.price.label"),
      to: url(ROUTES.PRICES),
    },
    {
      title: t("menu.resources.label"),
      to: url(ROUTES.RESOURCES),
    },
    {
      title: t("menu.company.label"),
      to: url(ROUTES.ABOUT_US),
    },
  ];

  return (
    <Grid container justifyContent="space-between" component="nav">
      {columns.map((column, index) => (
        <Grid item key={index} xs={12} md="auto">
          {column.to ? (
            <Typography variant="h6" component={Link} to={column.to} className="link">
              {t(column.title)}
            </Typography>
          ) : (
            <Typography variant="h6">{t(column.title)}</Typography>
          )}
          <List>
            {column.links?.map((child, index) => (
              <ListItem
                key={index}
                sx={{
                  padding: 0,
                  marginY: 1,
                }}
              >
                <Typography component={Link} to={child.to} className="link">
                  {t(child.title)}
                </Typography>
              </ListItem>
            ))}
          </List>
        </Grid>
      ))}
    </Grid>
  );
}

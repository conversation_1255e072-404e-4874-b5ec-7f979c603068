import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { LogoContainer } from "@/components/ui/common.styled";
import styled from "@emotion/styled";
import { useData } from "@/contexts/DataProvider";

const LogoImage = styled("img")(() => ({
  height: "100%",
  width: "100%",
}));

export default function FollowUs() {
  const { t } = useTranslation();
  const { socials } = useData();

  return (
    <Box sx={{
      display: "flex",
      flexDirection: "column",
      gap: 1,
    }}>
      <Typography sx={{ fontWeight: "bold" }}>
        {t("layout.footer.footer.follow_us")}
      </Typography>
      <Box sx={{ display: "flex", gap: 2 }}>
        {socials?.filter(social => null !== social.url).map(social => (
          <Link key={social.uuid} to={social.url} title={social.name} target="blank">
            <LogoContainer>
              <LogoImage src={social.logo} alt="" />
            </LogoContainer>
          </Link>
        ))}
      </Box>
    </Box>
  );
}

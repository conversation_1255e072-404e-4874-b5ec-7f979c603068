import AboutUs from "@assets/photos/visuel-about-us.png";
import GridBackground from "@assets/background/grid-background.png";
import { Box, Chip, Container, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

export default function Header() {
  const { t } = useTranslation();

  return (
    <Box sx={{
      backgroundImage: `url(${GridBackground})`,
      backgroundSize: "cover",
      backgroundPosition: "center",
      backgroundRepeat: "no-repeat",
      paddingY: 4,
    }}>
      <Container maxWidth="md" sx={{
        display: "flex",
        flexDirection: "column",
        gap: 6,
      }}>
        <Box sx={{
          textAlign: "center",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: 2,
        }}>
          <Typography variant="h1">{t("about_us.first_title")}</Typography>
          <Chip color="secondary" label={t("about_us.tag")} />
        </Box>
        <Box>
          <img src={AboutUs} width={"100%"} height={"100%"} alt="Banner about us" aria-hidden={true} />
        </Box>
      </Container>
    </Box>
  );
}

import { Box, Button, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { ChipPrimaryContrast } from "@/components/ui/chips.styled";
import ROUTES from "../../enums/ROUTES.js";
import BlockPartners from "../BlockPartners.jsx";
import { Link } from "react-router-dom";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function Partners() {
  const { t } = useTranslation();
  const { url } = useRouting();

  return (
    <Box sx={{
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      gap: 6,
    }}>
      <Box sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        textAlign: "center",
        gap: 3,
        width: { xs: "100%", xl: "640px" },
      }}>
        <ChipPrimaryContrast label={t("about_us.partners.tag")} />
        <Typography variant="h2">{t("about_us.partners.title")}</Typography>
      </Box>
      <BlockPartners />
      <Button
        component={Link}
        to={url(ROUTES.CONTACT)}
        sx={{ width: { xs: "100%", md: "max-content" } }}
      >
        {t("about_us.partners.btn_become_partner")}
      </Button>
    </Box>
  );
}

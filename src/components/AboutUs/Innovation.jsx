import { useTranslation } from "react-i18next";
import { <PERSON>, Card, CardContent, Chip, Grid, Typography } from "@mui/material";
import SinfinLogo from "@assets/logo/sinfin.svg?react";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import { useData } from "@/contexts/DataProvider";

export default function Innovation() {
  const { t } = useTranslation();
  const { trans } = useRouting();
  const { innovations } = useData();

  return (
    <Grid container spacing={4}>
      <Grid
        item
        xs={12}
        md={4}
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 2,
          zIndex: 2,
        }}
      >
        <Chip color="secondary" label={t("about_us.article_2.tag")} />
        <Typography variant="h3">{t("about_us.article_2.title")}</Typography>
        <Typography>{t("about_us.article_2.text")}</Typography>
      </Grid>
      <Grid item xs={12} md>
        <Grid container spacing={4} sx={{ position: "relative" }}>
          <Box
            sx={{
              position: "absolute",
              height: "100%",
              width: "100%",
              left: { xs: 0, md: 25 },
              color: (theme) => theme.palette.secondary[100],
              zIndex: 1,
            }}
          >
            <SinfinLogo width="100%" height="100%" />
          </Box>
          {innovations.map((innovation, i) => (
            <Grid
              key={innovation.uuid}
              item
              xs={12}
              md={6}
              sx={{
                zIndex: 5,
                display: "flex",
              }}
            >
              <Card
                sx={{
                  flexGrow: 1,
                  padding: 2,
                  border: (theme) => `1px solid ${theme.palette.secondary[200]}`,
                }}
              >
                <CardContent
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 1,
                  }}
                >
                  <Typography variant="h4">
                    {i + 1}. {trans(innovation.title)}
                  </Typography>
                  <Typography>{trans(innovation.description)}</Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Grid>
    </Grid>
  );
}

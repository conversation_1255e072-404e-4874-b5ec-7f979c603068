import { Box, Button, Grid, Typography, useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";
import ROUTES from "../../enums/ROUTES.js";
import { Link } from "react-router-dom";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import styled from "@emotion/styled";
import { useData } from "@/contexts/DataProvider.jsx";

const JobImage = styled("img")(({ theme }) => ({
  height: "100%",
  width: "100%",
  objectFit: "cover",
  borderRadius: theme.spacing(2),
}));

export default function Job() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { url, trans } = useRouting();
  const { jobs } = useData();

  return (
    <Box
      component="section"
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: { xs: 5, xl: 6 },
      }}
    >
      <Typography variant="h2">{t("about_us.job.title")}</Typography>
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          gap: 4,
          flexDirection: { xs: "column", sm: "row" },
          width: "100%",
        }}
      >
        {jobs.map((job) => (
          <Box
            key={job.uuid}
            sx={{
              [theme.breakpoints.down("sm")]: {
                height: "200px",
                width: "100%",
              },
              height: "334px",
              width: "max-content",
            }}
          >
            <JobImage src={job.image} alt={trans(job.name)} />
          </Box>
        ))}
      </Box>
      <Grid container maxWidth="md" justifyContent="space-between" gap={5}>
        <Grid item xs={12} xl={6}>
          <Typography>{t("about_us.job.text")}</Typography>
        </Grid>
        <Grid
          item
          xs={12}
          xl={4}
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: {
              xs: "center",
              xl: "start",
            },
            gap: 3,
          }}
        >
          <Typography variant="h4">{t("about_us.job.btn_join_us_label")}</Typography>
          <Button
            component={Link}
            to={url(ROUTES.CONTACT)}
            sx={{
              width: {
                xs: "100%",
                md: "max-content",
              },
            }}
          >
            {t("about_us.job.btn_join_us")}
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
}

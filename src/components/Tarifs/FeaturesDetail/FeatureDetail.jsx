import { Box } from "@mui/material";
import theme from "../../../theme";

export default function FeaturesDetail() {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        textAlign: "center",
        justifyContent: "center",
        gap: (theme) => theme.spacing(6),
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          textAlign: "center",
          justifyContent: "center",
          gap: (theme) => theme.spacing(6),
          [theme.breakpoints.down("xl")]: {
            display: "none",
          },
        }}
      >
        <Box
          sx={{
            display: "flex",
            gap: (theme) => theme.spacing(2),
            justifyContent: "center",
          }}
        >
        </Box>
      </Box>
    </Box>
  );
}

import { <PERSON>, Button, Card, CardContent, Typography } from "@mui/material";
import theme from "../../../theme/theme";
import { useTranslation } from "react-i18next";
import { Link as RouterLink } from "react-router-dom";
import ROUTES from "../../../enums/ROUTES.js";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function PricingCard({
  title,
  subtitle,
  price,
  details,
  description,
  priceStart,
  monthly,
  context = "light",
  showButton,
  titleDescription,
  skuNumber,
}) {
  const { t } = useTranslation();
  const { url } = useRouting();

  return (
    <Card sx={{
      backgroundColor: context === "dark" ? "primary.900" : "common.white",
      padding: (theme) => theme.spacing(4),
      borderRadius: (theme) => theme.spacing(2),
      border: (theme) => `solid 1px ${theme.palette.secondary[200]}`,
      [theme.breakpoints.down("xl")]: {
        padding: (theme) => theme.spacing(3),
      },
      flexGrow: 1,
    }}>
      <CardContent
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 3,
        }}
      >
        <Box>
          <Typography
            variant="h2"
            color={context === "dark" ? "common.white" : "common.black"}
          >
            {title}
          </Typography>
          <Typography
            color={context === "dark" ? "secondary.100" : "secondary.700"}
          >
            {subtitle}
          </Typography>
        </Box>
        <Box>
          {priceStart ? (
            <Typography
              variant="caption"
              component="span"
              color={context === "dark" ? "secondary.100" : "secondary.700"}
            >
              {priceStart}
            </Typography>
          ) : null}
          {price ? (
            <Typography
              variant="h1"
              color={context === "dark" ? "primary.100" : "primary.700"}
              style={{ whiteSpace: "nowrap" }}
            >
              {price}
              <Typography
                variant="caption"
                component="span"
                color={context === "dark" ? "primary.100" : "primary.700"}
                style={{ marginLeft: "0.5rem" }}
              >
                {monthly}
              </Typography>
            </Typography>
          ) : null}
        </Box>
        <Box>
          {titleDescription ? (
            <Typography
              variant="body2"
              color={context === "dark" ? "secondary.100" : "secondary.700"}
            >
              {titleDescription}
            </Typography>
          ) : null}
          {description ? (
            <ul>
              {description?.map((option, i) => (
                <li key={i}>
                  <Typography
                    variant="body2"
                    color={
                      context === "dark" ? "secondary.100" : "secondary.700"
                    }
                  >
                    {option}
                  </Typography>
                </li>
              ))}
            </ul>
          ) : null}
        </Box>
        <Box>
          {skuNumber ? (
            <Typography
              variant="body2"
              color={context === "dark" ? "secondary.100" : "secondary.700"}
            >
              {skuNumber}
            </Typography>
          ) : null}
          {details ? (
            <Typography
              variant="body2"
              color={context === "dark" ? "secondary.100" : "secondary.700"}
            >
              {details}
            </Typography>
          ) : null}
        </Box>
        {showButton && (
          <Button
            component={RouterLink}
            to={url(ROUTES.CONTACT)}
            sx={{
              width: {
                xs: "100%",
                md: "max-content",
              },
              backgroundColor: "common.white",
              color: "common.black",
              "&:hover": {
                backgroundColor: "grey.300",
              },
            }}
          >
            {t("common.contact_us")}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

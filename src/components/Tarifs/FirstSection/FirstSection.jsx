import { <PERSON>, Chip, Grid, Tooltip, Typography } from "@mui/material";
import GridBackground from "@assets/background/grid-background.png";
import { useTranslation } from "react-i18next";
import PricingCard from "@/components/Tarifs/FirstSection/PricingCard";
import InfoIcon from "@mui/icons-material/Info";

export default function FirstSection() {
  const { t } = useTranslation();

  const options = [{
    title: t("tarifs.first_section.option_1.title"),
    subtitle: t("tarifs.first_section.option_1.subtitle"),
    priceStart: t("common.price_start_at"),
    price: "1 000 €",
    monthly: t("common.month"),
    details: t("tarifs.first_section.option_1.detail"),
    skuNumber: t("tarifs.first_section.option_1.skuNumber"),
    description: t("tarifs.first_section.option_1.description", {
      returnObjects: true,
    }),
  }, {
    title: t("tarifs.first_section.option_2.title"),
    subtitle: t("tarifs.first_section.option_2.subtitle"),
    priceStart: t("common.price_start_at"),
    price: "2 000 €",
    monthly: t("common.month"),
    titleDescription: t("tarifs.first_section.option_2.titleDescription"),
    details: t("tarifs.first_section.option_2.detail"),
    skuNumber: t("tarifs.first_section.option_2.skuNumber"),
    description: t("tarifs.first_section.option_2.description", {
      returnObjects: true,
    }),
  }, {
    title: t("tarifs.first_section.option_3.title"),
    subtitle: t("tarifs.first_section.option_3.subtitle"),
    price: t("tarifs.price_on_demand"),
    titleDescription: t("tarifs.first_section.option_3.titleDescription"),

    // details: t("tarifs.first_section.option_3.detail"),
    // subDetails: `${t("tarifs.first_section.option_3.subDetail")} 1200 €`,
  }];

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        gap: 6,
        backgroundPosition: "center",
        backgroundImage: `url(${GridBackground})`,
        backgroundSize: "contain",
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: 3,
          textAlign: "center",
        }}
      >
        <Chip color="secondary" label={t("tarifs.first_section.tag")} />
        <Typography variant="h1">{t("tarifs.first_section.title")}</Typography>
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: 1,
          }}
        >
          <Typography>{t("tarifs.first_section.subtitle")}</Typography>
          <Tooltip title={t("tarifs.first_section.tooltip")}>
            <InfoIcon />
          </Tooltip>
        </Box>
      </Box>
      <Grid container spacing={4}>
        {options.map((o, i) => (
          <Grid item xs={12} xl={4} key={i} display="flex">
            <PricingCard
              {...o}
              context={i === options.length - 1 && "dark"}
              showButton={i === 2}
            />
          </Grid>
        ))}
      </Grid>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "flex-start",
          alignItems: "flex-start",
        }}
      >
        <Typography
          dangerouslySetInnerHTML={{
            __html: t("tarifs.first_section.note"),
          }}
          textAlign="left"
          sx={{ width: "100%" }}
        />
        <Typography
          dangerouslySetInnerHTML={{
            __html: t("tarifs.first_section.note2"),
          }}
          textAlign="left"
          sx={{ width: "100%" }}
        />
      </Box>
    </Box>
  );
}

import { Box, Button, Grid, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import ExpandFaq from "@/components/ui/ExpandFaq";
import ROUTES from "../../enums/ROUTES.js";
import { Link } from "react-router-dom";
import { useRouting } from "@/contexts/RoutingProvider.jsx";
import { useData } from "@/contexts/DataProvider.jsx";

export default function Faq() {
  const { t } = useTranslation();
  const { url, trans } = useRouting();
  const { faqs } = useData();

  console.log(faqs);

  return (
    <Grid container spacing={3} justifyContent="space-between" component={"section"}>
      <Grid
        item
        xs={12}
        xl={6}
        sx={{
          gap: (theme) => theme.spacing(3),
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Typography variant="h2">{t("index.faq.title")}</Typography>
        <Typography>{t("index.faq.subtitle")}</Typography>
        <Box sx={{ display: "flex", flexDirection: "column" }} />
        <Button sx={{ width: "max-content" }} component={Link} to={url(ROUTES.CONTACT)}>
          {t("index.faq.btn_contact")}
        </Button>
      </Grid>
      <Grid container item xs={12} xl={5} spacing={2}>
        {faqs.map((faq,j) => {
          const description = trans(faq.description);

          return (
            <Grid item xs={12} key={j}>
              <ExpandFaq
                title={trans(faq.title)}
                content={
                  <div>
                    {Array.isArray(description) ? (
                      <ul>
                        {description.map((item, i) => (
                          <li key={i} dangerouslySetInnerHTML={{ __html: item }} />
                        ))}
                      </ul>
                    ) : (
                      <div dangerouslySetInnerHTML={{ __html: description }} />
                    )}
                  </div>
                }
              />
            </Grid>
          );
        })}
      </Grid>
    </Grid>
  );
}

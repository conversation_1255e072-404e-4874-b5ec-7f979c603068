import HomeImage from "@assets/home/<USER>";
import GridBackground from "@assets/background/grid-background.png";
import { Box, Button, Grid, Typography, useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";
import CustomersList from "./CustomersList";
import ROUTES from "../../enums/ROUTES.js";
import { colorWords } from "@/utils/string.utils";
import { Link } from "react-router-dom";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function FirstSection() {
  const theme = useTheme();
  const { t } = useTranslation();
  const { url } = useRouting();

  return (
    <Box
      sx={{
        backgroundImage: `url(${GridBackground})`,
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
        display: "flex",
        flexDirection: "column",
        gap: theme.spacing(4),
        [theme.breakpoints.up("xl")]: {
          marginTop: theme.spacing(10),
        },
      }}
    >
      <Grid
        container
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: theme.spacing(4),
          [theme.breakpoints.up("xl")]: {
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          },
          padding: "32px 16px",
        }}
      >
        <Grid
          item
          xl={6}
          display="flex"
          flexDirection="column"
          gap={theme.spacing(2)}
        >
          <Typography
            variant="h1"
            dangerouslySetInnerHTML={{
              __html: colorWords(
                t("index.first_section.title"),
                t("index.first_section.title_colored_words").split(" "),
                theme.palette.primary.main,
              ),
            }}
          />
          <Typography>{t("index.first_section.subtitle")}</Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: theme.spacing(2),
              [theme.breakpoints.up("xl")]: {
                flexDirection: "row",
              },
            }}
          >
            <Button
              variant="contained"
              color="primary"
              sx={{
                [theme.breakpoints.down("md")]: {
                  width: "100%",
                },
              }}
              component={Link}
              to={url(ROUTES.ASK_DEMO)}
            >
              {t("common.ask_demo")}
            </Button>
          </Box>
        </Grid>
        <Grid
          item
          xl={5}
          sx={{
            display: "none",
            [theme.breakpoints.up("xl")]: {
              display: "block",
            },
          }}
        >
          <img src={HomeImage} height={360} alt="Banner home" aria-hidden={true} />
        </Grid>
      </Grid>
      <CustomersList />
    </Box>
  );
}

import { Button, Grid, Typography, useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";
import ROUTES from "../../enums/ROUTES.js";
import { Link } from "react-router-dom";
import { useRouting } from "@/contexts/RoutingProvider.jsx";

export default function StartNow() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { url } = useRouting();

  return (
    <Grid
      container
      alignItems={"center"}
      component={"section"}
      sx={{
        padding: (theme) => theme.spacing(5),
        backgroundColor: (theme) => theme.palette.primary[900],
        color: "secondary.100",
        borderRadius: (theme) => theme.spacing(3),
        gap: (theme) => theme.spacing(3),
      }}
    >
      <Grid item xs={12} lg={6}>
        <Typography variant="h4" color="inherit">{t("index.start_now.title")}</Typography>
        <Typography color="inherit">{t("index.start_now.subtitle")}</Typography>
      </Grid>
      <Grid item xs={12} lg={5} sx={{
        display: "flex",
        justifyContent: "flex-end",
        [theme.breakpoints.down("md")]: {
          justifyContent: "flex-start",
        },
      }}>
        <Button
          sx={{ width: "max-content" }}
          component={Link}
          to={url(ROUTES.ASK_DEMO)}
        >
          {t("common.ask_demo")}
        </Button>
      </Grid>
    </Grid>
  );
}

import { useData } from "@/contexts/DataProvider";
import { Box, Grid } from "@mui/material";

export default function CustomersList() {
  const { customers } = useData();

  return (
    <Box sx={{ justifyContent: "center", padding: 2 }}>
      <Grid container spacing={{ xs: 2, md: 4, xl: 6 }} justifyContent="center">
        {customers.map(customer => (
          <Grid item xs={4} sm={4} md={2} key={customer.uuid}>
            <Box
              sx={{
                display: "flex",
                maxHeight: "80px",
                maxWidth: "140px",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <img src={customer.logo} alt={customer.name} style={{
                height: "60px",
                width: "120px",
                maxHeight: "60px",
                maxWidth: "120px",
              }} />
            </Box>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}

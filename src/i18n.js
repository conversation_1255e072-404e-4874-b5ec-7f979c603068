import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import translationEN from "/src/langs/en.json";
import translationFR from "/src/langs/fr.json";
import { LANG_DEFAULT } from "@/enums/LANGS.js";

i18n
  // .use(LanguageDetector)
  .use(initReactI18next) // passes i18n down to react-i18next
  .init({
    resources: {
      en: { translation: translationEN },
      fr: { translation: translationFR },
    },
    fallbackLng: LANG_DEFAULT,
    debug: false,
    interpolation: {
      escapeValue: false, // react already safes from xss
    },
  });

export default i18n;

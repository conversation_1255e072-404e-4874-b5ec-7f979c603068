class MapperUtils {
  partner<PERSON><PERSON><PERSON>(partner) {
    return {
      uuid: partner.uuid,
      name: partner.name,
      logo: partner.logo,
    };
  }

  reviewToO(review) {
    return {
      uuid: review.uuid,
      review: review.review,
      job: review.job,
      author: review.author,
      image: review.image,
      isDefault: review.isDefault,
    };
  }

  faqToO(faq) {
    return {
      uuid: faq.uuid,
      title: {
        fr: faq.title.fr,
        en: faq.title.en,
      },
      description: {
        fr: faq.description.fr,
        en: faq.description.en,
      },
    };
  }

  customerToO(customer) {
    return {
      uuid: customer.uuid,
      name: customer.name,
      logo: customer.logo,
    };
  }

  glossaryToO(glossary) {
    return {
      uuid: glossary.uuid,
      name: glossary.name,
      description: glossary.description,
    };
  }

  innovationToO(value) {
    return {
      uuid: value.uuid,
      title: value.title,
      description: value.description,
    };
  }

  jobToO(job) {
    return {
      uuid: job.uuid,
      name: job.name,
      image: job.image,
    };
  }

  resourceToO(resource) {
    return {
      uuid: resource.uuid,
      title: resource.title,
      tags: resource.tags,
      metaTitle: resource.meta_title,
      metaDescription: resource.meta_description,
      image: resource.image,
      imageAlt: resource.image_alt,
      slug: resource.slug,
      createdAt: resource.createdAt,
      updatedAt: resource.updatedAt,
      content: resource.content,
    };
  }

  socialToO(social) {
    return {
      uuid: social.uuid,
      name: social.name,
      url: social.url,
      logo: social.logo,
      share: social.share,
    };
  }

  solutionToO(solution) {
    return {
      uuid: solution.uuid,
      title: solution.title,
      description: solution.description,
      image: solution.image,
      link: solution.link,
      route: solution.route,
    };
  }
}

const Mapper = new MapperUtils();

export default Mapper;

import { SELSSY_ENDPOINT } from "@/env.js";

export const sendToSelssy = async (data) => {
  return await fetch(SELSSY_ENDPOINT, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
};

export const get = async (endpoint) => {
  try {
    const response = await fetch(endpoint, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return await response.json();
  } catch (error) {
    console.error("Fetch error:", error.message);
  }
};

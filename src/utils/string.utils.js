/**
 * Colors specific words in a given text.
 *
 * This function takes a string of text, an array of words to be colored, and a color.
 * It returns the text with the specified words wrapped in a `<span>` tag with the given color.
 *
 * @param {string} text - The text in which to color the specified words.
 * @param {string[]} words - An array of words to be colored.
 * @param {string} color - The color to apply to the specified words.
 * @returns {string} The text with the specified words colored.
 *
 * @example
 * const text = "Hello world!";
 * const words = ["world"];
 * const color = "red";
 * const result = colorWords(text, words, color);
 * console.log(result); // "Hello <span style="color: red">world</span>!"
 */
export const colorWords = (text, words, color) => {
  const textArray = text.split(" ");
  const result = textArray.map((word) => {
    if (words.includes(word)) {
      return `<span style="color: ${color}">${word}</span>`;
    }
    return word;
  });
  return result.join(" ");
};

import { useTranslation } from "react-i18next";
import { createContext, useContext } from "react";
import { generatePath } from "react-router-dom";
import { LANG_DEFAULT } from "@/enums/LANGS.js";

// find current lang OR fallback to default lang
export const resolvePath = (route, lang) => {
  return route[lang] || route[LANG_DEFAULT] || "-";
};

export const generateUrlPath = (route, options, lang) => {
  // a way to override lang
  if (Object.prototype.hasOwnProperty.call(options, "lang") && route[options.lang]) {
    return route[options.lang];
  }

  return resolvePath(route, lang);
};

const RoutingContext = createContext({});

export const generateUrl = (route, options, lang) => {
  return generatePath(generateUrlPath(route, options, lang), { ...{ lang }, ...options });
};

export const useRouting = () => useContext(RoutingContext);

const LANG_TO_LOCALES = {
  fr: ["fr", "fr_FR"],
  en: ["en", "en_GB", "en_US"],
};

const extractLocaleDataFromArray = (array, langs) => {
  for (let lang of langs) {
    const locales = LANG_TO_LOCALES[lang] ?? [];
    for (let locale of locales) {
      for (let item of array) {
        if (item.locale === locale) {
          return item.data;
        }
      }
    }
  }

  return null;
};

export default function RoutingProvider({ children }) {
  const { i18n } = useTranslation();

  const url = (route, options = {}) => generateUrl(route, options, i18n.language);
  const trans = obj => {
    // as string
    if ("string" === typeof obj) {
      return obj;
    }

    // as array (platform)
    if (Array.isArray(obj)) {
      return extractLocaleDataFromArray(obj, [
        i18n.language,
        LANG_DEFAULT,
      ]);
    }

    // only objects are supported for now
    if ("object" !== typeof obj) {
      return "";
    }

    return obj[i18n.language] || obj[LANG_DEFAULT] || obj;
  };

  return (
    <RoutingContext.Provider value={{ url, trans }}>
      {children}
    </RoutingContext.Provider>
  );
};

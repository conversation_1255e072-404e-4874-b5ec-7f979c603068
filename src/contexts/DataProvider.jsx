import { createContext, useState, useContext, useEffect } from "react";
import { get } from "@/utils/api.utils";
import Mapper from "@/utils/mapper.utils";

const DataContext = createContext();

export const useData = () => useContext(DataContext);

export const DataProvider = ({ children }) => {
  const [resources, setResources] = useState([]);
  const [solutions, setSolutions] = useState([]);
  const [partners, setPartners] = useState([]);
  const [jobs, setJobs] = useState([]);
  const [innovations, setInnovations] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [faqs, setFaqs] = useState([]);
  const [socials, setSocials] = useState([]);
  const [reviews, setReviews] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        const resources = await get("/assets/contents/resources.json");
        setResources(resources.map((resource) => Mapper.resourceToO(resource)));

        const solutions = await get("/assets/contents/solutions.json");
        setSolutions(solutions.map((solution) => Mapper.solutionToO(solution)));

        const partners = await get("/assets/contents/partners.json");
        setPartners(partners.map((partner) => Mapper.partnerToO(partner)));

        const jobs = await get("/assets/contents/jobs.json");
        setJobs(jobs.map((job) => Mapper.jobToO(job)));

        const innovations = await get("/assets/contents/innovations.json");
        setInnovations(innovations.map((innovation) => Mapper.innovationToO(innovation)));

        const customers = await get("/assets/contents/customers.json");
        setCustomers(customers.map((customer) => Mapper.customerToO(customer)));

        const faqs = await get("/assets/contents/faqs.json");
        setFaqs(faqs.map((faq) => Mapper.faqToO(faq)));

        const socials = await get("/assets/contents/socials.json");
        setSocials(socials.map((social) => Mapper.socialToO(social)));

        const reviews = await get("/assets/contents/reviews.json");
        setReviews(reviews.map((review) => Mapper.reviewToO(review)));
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <DataContext.Provider
      value={{ resources, solutions, partners, jobs, innovations, customers, faqs, socials, reviews, isLoading, error }}
    >
      {children}
    </DataContext.Provider>
  );
};

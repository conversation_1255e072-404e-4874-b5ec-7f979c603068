import { ThemeProvider } from "@mui/material";
import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";
import ProductLayout from "./components/Products/ProductLayout";
import ResourcesLayout from "@/components/Resources/ResourcesLayout.jsx";
import SolutionsLayout from "./components/Solutions/SolutionsLayout";
import "./i18n";
import "./index.css";
import ROUTES from "./enums/ROUTES.js";
import theme from "./theme/theme";
import PageNotFound from "./pages/404";
import AboutUs from "./pages/AboutUs";
import Home from "./pages/Home";
import Dam from "./pages/Products/Dam";
import Hub from "./pages/Products/Hub";
import Pim from "./pages/Products/Pim";
import Workflow from "./pages/Products/Workflow";
import Syndication from "./pages/Products/Syndication";
import Completude from "./pages/Products/Completude";
import Location from "./pages/Products/Location";
import Connectors from "./pages/Products/Connectors";
import BrandPortal from "./pages/Products/BrandPortal";
import ResourceDetail from "@/pages/ResourceDetail.jsx";
import Resources from "@/pages/Resources.jsx";
import Ecommerce from "./pages/Solutions/Ecommerce";
import International from "./pages/Solutions/International";
import Marketplaces from "./pages/Solutions/Marketplaces";
import Replatforming from "./pages/Solutions/Replatforming";
import Luxe from "./pages/Solutions/Luxe";
import Agrifood from "./pages/Solutions/Agrifood";
import Retail from "./pages/Solutions/Retail";
import Industry from "./pages/Solutions/Industry";
import Prices from "@/pages/Prices.jsx";
import ScrollToTop from "./components/ScrollToTop";
import Layout from "./components/Layout/Layout.jsx";
import LANGS, { LANG_DEFAULT } from "./enums/LANGS.js";
import { Fragment } from "react";
import RoutingProvider, { resolvePath } from "@/contexts/RoutingProvider.jsx";
import Presentation from "./pages/Landing/Presentation";
import LayoutLanding from "./pages/Landing/LayoutLanding/LayoutLanding";
import PresentationDam from "./pages/LandingDam/PresentationDam";
import Glossaire from "./components/Resources/Glossaire";
import Content from "@/pages/Content.jsx";
import BlockContact from "@/components/BlockContact.jsx";
import contents from "@/data/contents.js";
import BlockDiscover from "@/components/BlockDiscover.jsx";
import LandingLeadGen from "./pages/LandingLeadGen";
import { DataProvider } from "./contexts/DataProvider";
import VideoDemo from "@/pages/VideoDemo.jsx";

export default function App() {
  return (
    <>
      <ThemeProvider theme={theme}>
        <DataProvider>
          <BrowserRouter>
            <RoutingProvider>
              <ScrollToTop />
              <Routes>
                {Object.values(LANGS).map((lang, index) => {
                  const urlFor = (route) => resolvePath(route, lang);

                  return (
                    <Fragment key={index}>
                      <Route path="/" element={<Navigate to={`/${LANG_DEFAULT}`} />} />
                      <Route path={"/:lang"} element={<Layout />}>
                        <Route path={"/:lang/video-de-demo"} element={<VideoDemo />} />
                        <Route path={`/:lang`} element={<Home />} />
                        <Route element={<ProductLayout />}>
                          <Route path={urlFor(ROUTES.DAM)} element={<Dam />} />
                          <Route path={urlFor(ROUTES.HUB)} element={<Hub />} />
                          <Route path={urlFor(ROUTES.PIM)} element={<Pim />} />
                          <Route path={urlFor(ROUTES.WORKFLOW)} element={<Workflow />} />
                          <Route path={urlFor(ROUTES.SYNDICATION)} element={<Syndication />} />
                          <Route path={urlFor(ROUTES.COMPLETUDE)} element={<Completude />} />
                          <Route path={urlFor(ROUTES.LOCATION)} element={<Location />} />
                          <Route path={urlFor(ROUTES.CONNECTORS)} element={<Connectors />} />
                          <Route path={urlFor(ROUTES.BRAND_PORTAL)} element={<BrandPortal />} />
                        </Route>
                        <Route element={<SolutionsLayout />}>
                          <Route path={urlFor(ROUTES.ECOMMERCE)} element={<Ecommerce />} />
                          <Route path={urlFor(ROUTES.INTERNATIONAL)} element={<International />} />
                          <Route path={urlFor(ROUTES.MARKETPLACES)} element={<Marketplaces />} />
                          <Route path={urlFor(ROUTES.REPLATFORMING)} element={<Replatforming />} />
                          <Route path={urlFor(ROUTES.LUXE)} element={<Luxe />} />
                          <Route path={urlFor(ROUTES.AGRIFOOD)} element={<Agrifood />} />
                          <Route path={urlFor(ROUTES.RETAIL)} element={<Retail />} />
                          <Route path={urlFor(ROUTES.INDUSTRY)} element={<Industry />} />

                        </Route>
                        <Route element={<ResourcesLayout />}>
                          <Route path={urlFor(ROUTES.RESOURCES_SLUG)} element={<ResourceDetail />} />
                          <Route path={urlFor(ROUTES.USECASE_SLUG)} element={<ResourceDetail />} />
                          <Route path={urlFor(ROUTES.RESOURCE_GLOSSAIRE)} element={<Glossaire />} />
                        </Route>
                        <Route path={urlFor(ROUTES.PRICES)} element={<Prices />} />
                        <Route path={urlFor(ROUTES.RESOURCES)} element={<Resources />} />
                        <Route path={urlFor(ROUTES.ABOUT_US)} element={<AboutUs />} />
                        <Route
                          path={urlFor(ROUTES.ASK_DEMO)}
                          element={
                            <Content
                              content={contents.find((c) => "5c229797-7d2a-499f-b1c5-749de4539763" === c.uuid)}
                              maxWidth="xl"
                            >
                              <BlockDiscover />
                            </Content>
                          }
                        />
                        <Route
                          path={urlFor(ROUTES.CONTACT)}
                          element={
                            <Content
                              content={contents.find((c) => "16b4769a-421e-415f-aec6-8029ae8f18e7" === c.uuid)}
                              maxWidth="xl"
                            >
                              <BlockContact />
                            </Content>
                          }
                        />
                        <Route
                          path={urlFor(ROUTES.LEGAL_NOTICE)}
                          element={
                            <Content
                              content={contents.find((c) => "f64aa72f-9872-4aa6-8f52-d426016aeceb" === c.uuid)}
                            />
                          }
                        />
                        <Route
                          path={urlFor(ROUTES.PRIVACY)}
                          element={
                            <Content
                              content={contents.find((c) => "8bf9512a-83d4-40d6-bea6-5ec7d49bdc88" === c.uuid)}
                            />
                          }
                        />
                      </Route>
                      <Route path={"/:lang"} element={<LayoutLanding />}>
                        <Route path={urlFor(ROUTES.LANDING)} element={<Presentation />} />
                      </Route>
                      <Route path={"/:lang"} element={<LayoutLanding />}>
                        <Route path={urlFor(ROUTES.LANDING_DAM)} element={<PresentationDam />} />
                      </Route>
                      {/* <Route path={"/:lang"} element={<LayoutLanding />}>
                        <Route path={urlFor(ROUTES.LANDING_LEAD_GEN)} element={<LandingLeadGen />} />
                      </Route> */}
                    </Fragment>
                  );
                })}
                <Route path="*" element={<PageNotFound />} />
              </Routes>
            </RoutingProvider>
          </BrowserRouter>
        </DataProvider>
      </ThemeProvider>
    </>
  );
}

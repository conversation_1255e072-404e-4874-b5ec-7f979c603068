# sinfin.fr

WIP Compilation is done in Gitlab when a tag is created.

- Tag your version (using Gitlab UI)
- A release will be created with a package

```bash
read -p "Tag? " TAG
mkdir -p /var/www/app/build
cd /var/www/app/build
curl "https://gitlab.com/api/v4/projects/56171543/packages/generic/sinfin/${TAG}/build.zip" -o build.zip
unzip build.zip
rm build.zip
cd ..
rm -rf online
mv build online
```

## staging

```bash
read -p "Tag? " TAG
mkdir -p /var/www/staging/build
cd /var/www/staging/build
curl "https://gitlab.com/api/v4/projects/56171543/packages/generic/sinfin/${TAG}/build.zip" -o build.zip
unzip build.zip
rm build.zip
cd ..
rm -rf online
mv build online
```

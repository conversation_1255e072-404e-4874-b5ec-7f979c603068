{"name": "sinfin-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build && node ./src/sitemap.js > dist/sitemap.xml", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "sitemap": "node ./src/sitemap.js"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.15.14", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "i18next": "^23.10.1", "i18next-browser-languagedetector": "^7.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-i18next": "^14.1.0", "react-id-swiper": "^4.0.0", "react-router-dom": "^6.22.3", "react-slick": "^0.30.2", "slick-carousel": "^1.8.1", "styled-components": "^6.1.8", "swiper": "^9.2.0", "vite-plugin-svgr": "^4.2.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "vite": "^5.2.0"}}